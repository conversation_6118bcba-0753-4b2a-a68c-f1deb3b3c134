import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip, ResponsiveContainer } from "recharts";
import "../../../styles/components/manager/ChartsSection.scss";

const ChartsSection = ({ bloodGroupData = [] }) => {
  // Colors for blood group chart - same as doctor dashboard
  const BLOOD_GROUP_COLORS = {
    "A+": "#D93E4C",
    "A-": "#20374E",
    "B+": "#DECCAA",
    "B-": "#D91022",
    "AB+": "#4caf50",
    "AB-": "#ff9800",
    "O+": "#2196f3",
    "O-": "#9c27b0",
  };

  return (
    <div className="charts-section">
      <div className="charts-grid single-chart">
        {/* Blood Group Distribution Chart */}
        <div className="chart-container">
          <div className="chart-header">
            <h3>Tỷ lệ nhóm máu trong kho</h3>
            <span className="chart-subtitle"><PERSON><PERSON> bố theo đơn vị máu</span>
          </div>
          <div className="chart-content">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={bloodGroupData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) =>
                    `${name} ${(percent * 100).toFixed(0)}%`
                  }
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {(bloodGroupData || []).map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={BLOOD_GROUP_COLORS[entry?.name] || "#8884d8"}
                    />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChartsSection;
