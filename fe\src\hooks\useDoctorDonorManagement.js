import { useState, useEffect } from "react";
import { message } from "antd";
import authService from "../services/authService";
import bloodDonationService from "../services/bloodDonationService";
import NotificationService from "../services/notificationService";
import { DOCTOR_TYPES } from "../services/mockData";

/**
 * Custom hook for managing doctor donor management logic
 */
export const useDoctorDonorManagement = () => {
  const [donors, setDonors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState("all");
  const [selectedDonor, setSelectedDonor] = useState(null);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);

  const [updateData, setUpdateData] = useState({
    bloodType: "",
    healthStatus: "",
    chronicDiseases: [],
    bloodRelatedDiseases: [],
    notes: "",
    testResults: {
      hemoglobin: "",
      bloodPressure: "",
      heartRate: "",
      temperature: "",
      weight: "",
      height: "",
    },
  });

  const [statusUpdateData, setStatusUpdateData] = useState({
    status: "",
    notes: "",
    healthCheck: {
      bloodPressure: "",
      heartRate: "",
      weight: "",
      height: "",
      hemoglobin: "",
      temperature: "",
    },
  });

  const currentUser = authService.getCurrentUser();
  const isBloodDepartment = currentUser?.doctorType === DOCTOR_TYPES.BLOOD_DEPARTMENT;

  // Load donors data
  const loadDonors = async () => {
    setLoading(true);
    try {
      const response = await bloodDonationService.getAllAppointments();
      console.log("Blood donation appointments response:", response);

      let appointmentsData = [];
      if (Array.isArray(response)) {
        appointmentsData = response;
      } else if (response.data && Array.isArray(response.data)) {
        appointmentsData = response.data;
      } else if (response.appointments && Array.isArray(response.appointments)) {
        appointmentsData = response.appointments;
      }

      const transformedDonors = await Promise.all(
        appointmentsData.map(async (appointment) => {
          try {
            let userInfo = {};
            const userId = appointment.UserId || appointment.userId || appointment.UserID;
            if (userId) {
              try {
                const userResponse = await bloodDonationService.getUserInfo(userId);
                userInfo = userResponse.data || userResponse;
              } catch (userError) {
                console.error(`Failed to fetch user info for userId ${userId}:`, userError);
                userInfo = {};
              }
            }

            return {
              id: appointment.AppointmentId || appointment.appointmentId || appointment.id,
              userId: userId,
              name: userInfo.Name || userInfo.name || appointment.Name || "N/A",
              phone: userInfo.Phone || userInfo.phone || appointment.Phone || "N/A",
              email: userInfo.Email || userInfo.email || appointment.Email || "N/A",
              bloodType: userInfo.BloodGroup || userInfo.bloodGroup || appointment.BloodGroup || "N/A",
              age: userInfo.Age || userInfo.age || appointment.Age || 0,
              gender: userInfo.Gender || userInfo.gender || appointment.Gender || "unknown",
              weight: userInfo.Weight || userInfo.weight || appointment.Weight || 0,
              height: userInfo.Height || userInfo.height || appointment.Height || 0,
              appointmentDate: appointment.AppointmentDate || appointment.appointmentDate || appointment.RequestedDonationDate,
              timeSlot: appointment.TimeSlot || appointment.timeSlot || "morning",
              status: appointment.Status !== undefined ? appointment.Status :
                appointment.status !== undefined ? appointment.status : 0,
              lastDonationDate: appointment.LastDonationDate || appointment.lastDonationDate || userInfo.LastDonationDate,
              notes: appointment.Notes || appointment.notes || "",
              createdAt: appointment.CreatedAt || appointment.createdAt || new Date().toISOString(),
              healthSurvey: {
                chronicDiseases: [],
                recentIllness: false,
                medications: "",
                allergies: "",
              },
              testResults: {
                hemoglobin: "",
                bloodPressure: "",
                heartRate: "",
                temperature: "",
                weight: (userInfo.Weight || userInfo.weight || appointment.Weight || "").toString(),
                height: (userInfo.Height || userInfo.height || appointment.Height || "").toString(),
              },
              healthStatus: "unknown",
              bloodRelatedDiseases: [],
              totalDonations: 0,
            };
          } catch (transformError) {
            console.error("Error transforming appointment data:", transformError);
            return null;
          }
        })
      );

      const validDonors = transformedDonors.filter(donor => donor !== null);
      setDonors(validDonors);

      if (validDonors.length === 0) {
        message.info("Không có dữ liệu người hiến máu");
      }
    } catch (error) {
      console.error("Error loading donors:", error);
      message.error("Có lỗi xảy ra khi tải dữ liệu người hiến máu");
      setDonors([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter donors based on current filter
  const getFilteredDonors = () => {
    const today = new Date().toISOString().split("T")[0];

    switch (filter) {
      case "today":
        return donors.filter((d) => d.appointmentDate === today);
      case "pending":
        return donors.filter((d) => {
          const status = typeof d.status === 'string' ? parseInt(d.status) : d.status;
          return status === 0;
        });
      case "approved":
        return donors.filter((d) => {
          const status = typeof d.status === 'string' ? parseInt(d.status) : d.status;
          return status === 2;
        });
      case "rejected":
        return donors.filter((d) => {
          const status = typeof d.status === 'string' ? parseInt(d.status) : d.status;
          return status === 1;
        });
      case "cancelled":
        return donors.filter((d) => {
          const status = typeof d.status === 'string' ? parseInt(d.status) : d.status;
          return status === 3;
        });
      default:
        return donors;
    }
  };

  // Calculate statistics
  const getStatistics = () => {
    const today = new Date().toISOString().split("T")[0];

    return {
      todayCount: donors.filter(d => d.appointmentDate === today).length,
      pendingCount: donors.filter(d => {
        const status = typeof d.status === 'string' ? parseInt(d.status) : d.status;
        return status === 0;
      }).length,
      approvedCount: donors.filter(d => {
        const status = typeof d.status === 'string' ? parseInt(d.status) : d.status;
        return status === 2;
      }).length,
      rejectedCount: donors.filter(d => {
        const status = typeof d.status === 'string' ? parseInt(d.status) : d.status;
        return status === 1;
      }).length,
      cancelledCount: donors.filter(d => {
        const status = typeof d.status === 'string' ? parseInt(d.status) : d.status;
        return status === 3;
      }).length,
      totalCount: donors.length
    };
  };

  // Handle update donor info
  const handleUpdateDonor = (donor) => {
    console.log("Opening update modal for donor:", donor);
    setSelectedDonor(donor);

    // Pre-fill form with existing donor data
    setUpdateData({
      bloodType: donor.bloodType || "",
      healthStatus: donor.healthStatus || "good", // Default to good if not set
      chronicDiseases: donor.healthSurvey?.chronicDiseases || [],
      bloodRelatedDiseases: donor.bloodRelatedDiseases || [],
      notes: donor.notes || "",
      testResults: {
        // Use existing values from donor or testResults
        hemoglobin: donor.testResults?.hemoglobin || "",
        bloodPressure: donor.testResults?.bloodPressure || "",
        heartRate: donor.testResults?.heartRate || "",
        temperature: donor.testResults?.temperature || "",
        weight: donor.weight || donor.testResults?.weight || "",
        height: donor.height || donor.testResults?.height || "",
      },
    });
    setShowUpdateModal(true);
  };

  // Handle update status
  const handleUpdateStatus = (donor) => {
    setSelectedDonor(donor);
    setStatusUpdateData({
      status: donor.status,
      notes: donor.notes || "",
      healthCheck: {
        bloodPressure: donor.testResults?.bloodPressure || "",
        heartRate: donor.testResults?.heartRate || "",
        weight: donor.weight || donor.testResults?.weight || "",
        height: donor.height || donor.testResults?.height || "",
        hemoglobin: donor.testResults?.hemoglobin || "",
        temperature: donor.testResults?.temperature || "",
      },
    });
    setShowStatusModal(true);
  };

  // Handle cancel appointment
  const handleDeleteAppointment = async (appointmentId) => {
    try {
      await bloodDonationService.deleteAppointment(appointmentId);
      setDonors(prev => prev.map(donor =>
        donor.id === appointmentId
          ? { ...donor, status: 3, cancelledAt: new Date().toISOString() }
          : donor
      ));
      message.success("Hủy lịch hẹn thành công!");
    } catch (error) {
      console.error("Error cancelling appointment:", error);
      message.error("Có lỗi xảy ra khi hủy lịch hẹn!");
    }
  };

  // Load data on mount
  useEffect(() => {
    if (isBloodDepartment) {
      loadDonors();
    }
  }, [isBloodDepartment]);

  return {
    // State
    donors,
    loading,
    filter,
    selectedDonor,
    showUpdateModal,
    showStatusModal,
    updateData,
    statusUpdateData,
    currentUser,
    isBloodDepartment,

    // Computed values
    filteredDonors: getFilteredDonors(),
    statistics: getStatistics(),

    // Actions
    setFilter,
    setShowUpdateModal,
    setShowStatusModal,
    setUpdateData,
    setStatusUpdateData,
    setSelectedDonor,
    loadDonors,
    handleUpdateDonor,
    handleUpdateStatus,
    handleDeleteAppointment,
  };
};
