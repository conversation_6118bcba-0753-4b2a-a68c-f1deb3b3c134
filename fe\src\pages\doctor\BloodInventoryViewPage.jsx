import React, { useState, useEffect } from "react";
import { ReloadOutlined, DatabaseOutlined } from "@ant-design/icons";
import DoctorLayout from "../../components/doctor/DoctorLayout";
import PageHeader from "../../components/doctor/PageHeader";
import BloodInventoryTable from "../../components/shared/BloodInventoryTable";
import { fetchBloodInventory } from "../../services/bloodInventoryService";
import {
  getBloodComponentName,
  getInventoryStatus,
  mapRhTypeToSymbol,
} from "../../constants/bloodInventoryConstants";
import "../../styles/components/DoctorPageHeader.scss";
import "../../styles/pages/BloodInventoryViewPage.scss";

const BloodInventoryViewPage = () => {
  const [inventory, setInventory] = useState([]);
  const [loading, setLoading] = useState(false);

  const loadInventory = async () => {
    try {
      setLoading(true);
      const data = await fetchBloodInventory();
      const inventoryWithStatus = data.map((item) => {
        const bloodType = `${item.bloodGroup}${mapRhTypeToSymbol(item.rhType)}`;
        const status = getInventoryStatus(item.quantity);
        return {
          ...item,
          bloodType,
          status,
          componentType: getBloodComponentName(item.componentId),
          inventoryId: item.InventoryID || item.inventoryId,
          bagType: item.bagType || "250ml",
        };
      });
      setInventory(inventoryWithStatus);
    } catch (err) {
      console.error("Failed to load inventory:", err);
      setInventory([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadInventory();
  }, []);

  return (
    <DoctorLayout>
      <div className="blood-inventory-view">
        <PageHeader
          title="Kho máu"
          description="Xem thông tin tồn kho máu và các thành phần máu"
          icon={DatabaseOutlined}
          actions={[
            {
              label: "Làm mới",
              icon: <ReloadOutlined />,
              onClick: loadInventory,
              loading: loading,
            },
          ]}
        />

        <div className="blood-inventory-view-content no-margin-padding">
          <BloodInventoryTable
            data={inventory}
            showActions={false} // Doctor không có quyền thao tác
            pagination={{ pageSize: 10 }}
            scroll={{ x: true }}
            loading={loading}
          />
        </div>
      </div>
    </DoctorLayout>
  );
};

export default BloodInventoryViewPage;
