import { useState, useEffect, useCallback } from "react";
import { bloodRequestService } from "../services/bloodRequestService";
import userInfoService from "../services/userInfoService";
import authService from "../services/authService";
import { DOCTOR_TYPES, URGENCY_LEVELS } from "../services/mockData";
import { COMPONENT_TYPES } from "../constants/bloodInventoryConstants";
import {
  classifyBloodRequest,
  isValidUserID,
} from "../utils/bloodRequestClassification";

/**
 * Custom hook for managing blood requests
 * Handles loading, creating, and managing blood requests for doctors
 */
export const useBloodRequestManagement = () => {
  const [requests, setRequests] = useState([]); // Internal requests (from doctors)
  const [externalRequests, setExternalRequests] = useState([]); // External requests (from members)
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("internal");

  const currentUser = authService.getCurrentUser();
  const isBloodDepartment =
    currentUser?.doctorType === DOCTOR_TYPES.BLOOD_DEPARTMENT;

  const loadBloodRequests = useCallback(async () => {
    try {
      setLoading(true);

      if (isBloodDepartment) {
        // Blood department doctors can see all requests and need to separate them by role
        const response = await bloodRequestService.getBloodRequests();

        if (response.success) {
          // Helper function to format Rh type
          const formatRhType = (rhType) => {
            if (!rhType) return "+";
            if (rhType === "Positive" || rhType === "Rh+" || rhType === "+")
              return "+";
            if (rhType === "Negative" || rhType === "Rh-" || rhType === "-")
              return "-";
            return "+"; // Default to positive
          };

          // Transform API data to match UI expectations
          const transformedRequests = response.data.map((request) => {
            return {
              ...request,
              id: request.requestId,
              requestID: request.requestId,
              userID: request.userId || request.userID,
              bloodType: `${request.bloodGroup}${formatRhType(request.rhType)}`,
              bloodTypeDisplay: `${request.bloodGroup || "O"}${formatRhType(
                request.rhType
              )}`,
              componentType: COMPONENT_TYPES.WHOLE,
              urgencyLevel: URGENCY_LEVELS.NORMAL,
              neededTime: request.createdTime,
              patientCode: request.patientId?.toString() || "",
              doctorInfo: {
                name: request.doctorName,
                department: currentUser?.department,
              },
              patientInfo: {
                name: request.patientName,
                age: request.age,
                gender: request.gender,
                recordId: request.patientId?.toString() || "",
              },
              hospitalInfo: {
                name: request.facilityName,
                department: currentUser?.department,
                address: "",
              },
            };
          });

          // Separate requests by user role using API
          const internalRequests = [];
          const externalRequests = [];

          // Get user info for each request to determine role
          for (const request of transformedRequests) {
            try {
              // Use classification utility to determine request type
              const classification = classifyBloodRequest(request);

              // Check if userID is valid before making API call
              if (!isValidUserID(request.userID)) {
                // Use classification result
                if (classification.isInternal) {
                  internalRequests.push({
                    ...request,
                    userRole: classification.userRole,
                    userRoleID: classification.userRoleID,
                  });
                } else {
                  externalRequests.push({
                    ...request,
                    userRole: classification.userRole,
                    userRoleID: classification.userRoleID,
                  });
                }
                continue;
              }

              const userInfo = await userInfoService.getUserInfo(
                request.userID
              );

              // roleID = 1: Member (External requests)
              // roleID = 2: Staff-Doctor (Internal requests)
              if (userInfo.roleID === 1) {
                externalRequests.push({
                  ...request,
                  userRole: "Member",
                  userRoleID: 1,
                });
              } else if (userInfo.roleID === 2) {
                internalRequests.push({
                  ...request,
                  userRole: "Staff-Doctor",
                  userRoleID: 2,
                });
              } else {
                // Default to external for other roles
                externalRequests.push({
                  ...request,
                  userRole: "Other",
                  userRoleID: userInfo.roleID,
                });
              }
            } catch (error) {
              // Use classification utility for fallback
              const classification = classifyBloodRequest(request);

              if (classification.isInternal) {
                internalRequests.push({
                  ...request,
                  userRole: classification.userRole,
                  userRoleID: classification.userRoleID,
                });
              } else {
                externalRequests.push({
                  ...request,
                  userRole: classification.userRole,
                  userRoleID: classification.userRoleID,
                });
              }
            }
          }

          setRequests(internalRequests);
          setExternalRequests(externalRequests);
        }
      } else {
        // Non-hematology doctors only see their own requests
        const response = await bloodRequestService.getBloodRequestsByUser(
          currentUser?.id
        );

        if (response.success) {
          // Helper function to format Rh type (same as above)
          const formatRhType = (rhType) => {
            if (!rhType) return "+";
            if (rhType === "Positive" || rhType === "Rh+" || rhType === "+")
              return "+";
            if (rhType === "Negative" || rhType === "Rh-" || rhType === "-")
              return "-";
            return "+"; // Default to positive
          };

          // Transform API data to match UI expectations
          const transformedRequests = response.data.map((request) => ({
            ...request,
            id: request.requestId,
            requestID: request.requestId,
            bloodType: `${request.bloodGroup}${formatRhType(request.rhType)}`,
            bloodTypeDisplay: `${request.bloodGroup || "O"}${formatRhType(
              request.rhType
            )}`,
            componentType: COMPONENT_TYPES.WHOLE,
            urgencyLevel: URGENCY_LEVELS.NORMAL,
            neededTime: request.createdTime,
            patientCode: request.patientId?.toString() || "",
            doctorInfo: {
              name: request.doctorName,
              department: currentUser?.department,
            },
            patientInfo: {
              name: request.patientName,
              age: request.age,
              gender: request.gender,
              recordId: request.patientId?.toString() || "",
            },
            hospitalInfo: {
              name: request.facilityName,
              department: currentUser?.department,
              address: "",
            },
          }));

          setRequests(transformedRequests);
        } else {
          console.error("Failed to load blood requests:", response.error);
          setRequests([]);
        }
      }
    } catch (error) {
      console.error("Error loading blood requests:", error);
      setRequests([]);
    } finally {
      setLoading(false);
    }
  }, [currentUser, isBloodDepartment]);

  useEffect(() => {
    loadBloodRequests();
  }, [loadBloodRequests]);

  const handleUpdateRequest = useCallback((updatedRequest) => {
    if (updatedRequest.requestType === "external") {
      setExternalRequests((prev) =>
        prev.map((req) => (req.id === updatedRequest.id ? updatedRequest : req))
      );
    } else {
      setRequests((prev) =>
        prev.map((req) => (req.id === updatedRequest.id ? updatedRequest : req))
      );
    }
  }, []);

  const handleApproveExternal = useCallback(
    async (requestId) => {
      try {
        // Find the request data to pass to API
        const requestData =
          externalRequests.find(
            (req) => req.id === requestId || req.requestId === requestId
          ) ||
          requests.find(
            (req) => req.id === requestId || req.requestId === requestId
          );

        // Call API to accept the request using PATCH status endpoint
        const response = await bloodRequestService.updateBloodRequestStatus(
          requestId,
          1, // Status: Accepted
          `Yêu cầu được chấp nhận bởi ${
            currentUser?.name || "Bác sĩ khoa huyết học"
          }`
        );

        if (response.success) {
          // Update local state on success
          setExternalRequests((prev) =>
            prev.map((req) => {
              const shouldUpdate =
                req.id === requestId ||
                req.requestId === requestId ||
                req.requestID === requestId ||
                req.id === parseInt(requestId) ||
                req.requestId === parseInt(requestId);

              if (shouldUpdate) {
                return {
                  ...req,
                  status: 1, // Accepted
                  acceptedBy: currentUser?.name,
                  acceptedAt: new Date().toISOString(),
                  acceptedNotes: `Yêu cầu được chấp nhận bởi ${
                    currentUser?.name || "Bác sĩ khoa huyết học"
                  }`,
                };
              }
              return req;
            })
          );

          setRequests((prev) =>
            prev.map((req) => {
              const shouldUpdate =
                req.id === requestId ||
                req.requestId === requestId ||
                req.requestID === requestId ||
                req.id === parseInt(requestId) ||
                req.requestId === parseInt(requestId);

              if (shouldUpdate) {
                return {
                  ...req,
                  status: 1, // Accepted
                  acceptedBy: currentUser?.name,
                  acceptedAt: new Date().toISOString(),
                  acceptedNotes: `Yêu cầu được chấp nhận bởi ${
                    currentUser?.name || "Bác sĩ khoa huyết học"
                  }`,
                };
              }
              return req;
            })
          );

          await loadBloodRequests();
          alert("Đã chấp nhận yêu cầu máu thành công!");
        } else {
          alert("Có lỗi xảy ra khi chấp nhận yêu cầu: " + response.error);
        }
      } catch (error) {
        console.error("Error accepting blood request:", error);
        alert("Có lỗi xảy ra khi chấp nhận yêu cầu máu");
      }
    },
    [currentUser, loadBloodRequests, externalRequests, requests]
  );

  const handleRejectExternal = useCallback(
    async (requestId, reason) => {
      try {
        // Find the request data to pass to API
        const requestData =
          externalRequests.find(
            (req) => req.id === requestId || req.requestId === requestId
          ) ||
          requests.find(
            (req) => req.id === requestId || req.requestId === requestId
          );

        // Call API to reject the request using PATCH status endpoint
        const response = await bloodRequestService.updateBloodRequestStatus(
          requestId,
          3, // Status: Rejected
          `Yêu cầu bị từ chối bởi ${
            currentUser?.name || "Bác sĩ khoa huyết học"
          }. Lý do: ${reason}`
        );

        if (response.success) {
          // Update local state on success
          setExternalRequests((prev) =>
            prev.map((req) => {
              const shouldUpdate =
                req.id === requestId ||
                req.requestId === requestId ||
                req.requestID === requestId ||
                req.id === parseInt(requestId) ||
                req.requestId === parseInt(requestId);

              if (shouldUpdate) {
                return {
                  ...req,
                  status: 3, // Rejected
                  rejectionReason: reason,
                  rejectedBy: currentUser?.name,
                  rejectedAt: new Date().toISOString(),
                  rejectedNotes: `Yêu cầu bị từ chối bởi ${
                    currentUser?.name || "Bác sĩ khoa huyết học"
                  }. Lý do: ${reason}`,
                };
              }
              return req;
            })
          );

          setRequests((prev) =>
            prev.map((req) => {
              const shouldUpdate =
                req.id === requestId ||
                req.requestId === requestId ||
                req.requestID === requestId ||
                req.id === parseInt(requestId) ||
                req.requestId === parseInt(requestId);

              if (shouldUpdate) {
                return {
                  ...req,
                  status: 3, // Rejected
                  rejectionReason: reason,
                  rejectedBy: currentUser?.name,
                  rejectedAt: new Date().toISOString(),
                  rejectedNotes: `Yêu cầu bị từ chối bởi ${
                    currentUser?.name || "Bác sĩ khoa huyết học"
                  }. Lý do: ${reason}`,
                };
              }
              return req;
            })
          );

          await loadBloodRequests();
          alert("Đã từ chối yêu cầu máu thành công!");
        } else {
          alert("Có lỗi xảy ra khi từ chối yêu cầu: " + response.error);
        }
      } catch (error) {
        console.error("Error rejecting blood request:", error);
        alert("Có lỗi xảy ra khi từ chối yêu cầu máu");
      }
    },
    [currentUser, loadBloodRequests, externalRequests, requests]
  );

  const handleCompleteRequest = useCallback(
    async (requestId) => {
      try {
        // Find the request data to pass to API
        const requestData =
          externalRequests.find(
            (req) => req.id === requestId || req.requestId === requestId
          ) ||
          requests.find(
            (req) => req.id === requestId || req.requestId === requestId
          );

        // Call API to complete the request
        const response = await bloodRequestService.completeBloodRequest(
          requestId,
          requestData
        );

        if (response.success) {
          // Update local state on success
          setExternalRequests((prev) =>
            prev.map((req) =>
              req.id === requestId
                ? {
                    ...req,
                    status: 2, // Completed
                    processedBy: currentUser?.name,
                    completedAt: new Date().toISOString(),
                  }
                : req
            )
          );

          // Also update internal requests if needed
          setRequests((prev) =>
            prev.map((req) =>
              req.id === requestId
                ? {
                    ...req,
                    status: 2, // Completed
                    processedBy: currentUser?.name,
                    completedAt: new Date().toISOString(),
                  }
                : req
            )
          );

          // Reload data to get latest from server
          await loadBloodRequests();

          // Show success message
          alert("Đã hoàn thành yêu cầu máu thành công!");
        } else {
          // Show error message
          alert("Có lỗi xảy ra khi hoàn thành yêu cầu: " + response.error);
        }
      } catch (error) {
        console.error("Error completing blood request:", error);
        alert("Có lỗi xảy ra khi hoàn thành yêu cầu máu");
      }
    },
    [currentUser, loadBloodRequests, externalRequests, requests]
  );

  return {
    requests,
    externalRequests,
    loading,
    activeTab,
    setActiveTab,
    isBloodDepartment,
    currentUser,
    loadBloodRequests,
    handleUpdateRequest,
    handleApproveExternal,
    handleRejectExternal,
    handleCompleteRequest,
  };
};
