import React from "react";
import {
  Form,
  InputNumber,
  Radio,
  Checkbox,
  DatePicker,
  Button,
  Alert,
  Card,
  Row,
  Col,
  Space,
  Typography,
  Divider,
  Input,
} from "antd";
import {
  InfoCircleOutlined,
  HeartOutlined,
  ArrowLeftOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";

const { Title, Text } = Typography;

/**
 * Component hiển thị bước 2: Khảo sát sức khỏe
 */
const HealthSurveyStep = ({
  personalInfo,
  healthSurvey,
  weightError,
  heightError,
  loading,
  onHealthSurveyChange,
  onCheckboxChange,
  onSubmit,
  onBack,
}) => {
  return (
    <Card
      title={
        <Space>
          <HeartOutlined />
          <span>Khảo sát sức khỏe</span>
        </Space>
      }
      className="health-survey-card"
    >
      <Text className="survey-description">
        Vui lòng trả lời các câu hỏi sau để đánh gi<PERSON> tình trạng sức khỏe
      </Text>

      <Form layout="vertical">
        {/* Basic Health Info */}
        <Title level={4} className="section-title">
          Thông tin cơ bản
        </Title>

        {personalInfo.bloodType && (
          <Alert
            message="Nhóm máu được lấy từ hồ sơ cá nhân"
            description={`Nhóm máu: ${personalInfo.bloodType}`}
            type="info"
            icon={<InfoCircleOutlined />}
            className="blood-type-alert"
          />
        )}

        {/* Thông báo về thông tin sức khỏe */}
        <Alert
          message="Thông tin sức khỏe cơ bản"
          description={
            <div>
              {healthSurvey.weight ? (
                <div>• Cân nặng: {healthSurvey.weight} kg (từ hồ sơ - có thể chỉnh sửa)</div>
              ) : (
                <div>• Cân nặng: chưa có thông tin - <strong>bắt buộc nhập</strong></div>
              )}
              {healthSurvey.height ? (
                <div>• Chiều cao: {healthSurvey.height} cm (từ hồ sơ - có thể chỉnh sửa)</div>
              ) : (
                <div>• Chiều cao: chưa có thông tin - <strong>bắt buộc nhập</strong></div>
              )}
            </div>
          }
          type="info"
          icon={<InfoCircleOutlined />}
          className="health-info-alert"
          style={{ marginBottom: 16 }}
        />

        <Row gutter={16}>
          <Col xs={24} md={12}>
            <div id="weight-section">
              <Form.Item
                label="Cân nặng (kg)"
                required
                validateStatus={weightError ? "error" : ""}
                help={weightError}
              >
                <InputNumber
                  value={healthSurvey.weight}
                  onChange={(value) => onHealthSurveyChange("weight", value)}
                  placeholder="Nhập cân nặng"
                  min={42}
                  max={200}
                  className="input-number"
                  addonAfter="kg"
                  status={weightError ? "error" : ""}
                />
                {!weightError && (
                  <div
                    style={{
                      fontSize: "12px",
                      color: "#666",
                      marginTop: "4px",
                      fontStyle: "italic",
                    }}
                  >
                    Cân nặng: ≥{" "}
                    {personalInfo.gender === "female"
                      ? "42 kg với nữ"
                      : "45 kg với nam"}
                  </div>
                )}
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item
              label="Chiều cao (cm)"
              required
              validateStatus={heightError ? "error" : ""}
              help={heightError}
            >
              <InputNumber
                value={healthSurvey.height}
                onChange={(value) => onHealthSurveyChange("height", value)}
                placeholder="Nhập chiều cao"
                min={100}
                max={250}
                className="input-number"
                addonAfter="cm"
                status={heightError ? "error" : ""}
              />
            </Form.Item>
          </Col>
        </Row>

        <Divider />

        {/* Question 1 */}
        <div id="question-1">
          <Title level={5}>1. Anh/chị từng hiến máu chưa?</Title>
          <Form.Item>
            <Radio.Group
              value={healthSurvey.hasDonatedBefore}
              onChange={(e) =>
                onHealthSurveyChange("hasDonatedBefore", e.target.value)
              }
            >
              <Radio value={true}>Có</Radio>
              <Radio value={false}>Không</Radio>
            </Radio.Group>
          </Form.Item>

          {healthSurvey.hasDonatedBefore === true && (
            <Form.Item label="Ngày hiến máu gần nhất" required>
              <DatePicker
                value={
                  healthSurvey.lastDonationDate
                    ? dayjs(healthSurvey.lastDonationDate)
                    : null
                }
                onChange={(date) => {
                  if (date && date.isAfter(dayjs())) {
                    alert("Không được chọn ngày trong tương lai!");
                    onHealthSurveyChange("lastDonationDate", "");
                  } else {
                    onHealthSurveyChange(
                      "lastDonationDate",
                      date ? date.format("YYYY-MM-DD") : ""
                    );
                  }
                }}
                disabledDate={(current) =>
                  current && current > dayjs().endOf("day")
                }
                className="datepicker-full"
                placeholder="Chọn ngày hiến máu gần nhất"
                format="DD/MM/YYYY"
              />
            </Form.Item>
          )}
        </div>

        <Divider />

        {/* Question 2 */}
        <div id="question-2">
          <Title level={5}>
            2. Hiện tại, anh/chị có mắc bệnh lý nào không?
          </Title>
          <Form.Item>
            <Radio.Group
              value={healthSurvey.hasCurrentMedicalConditions}
              onChange={(e) =>
                onHealthSurveyChange(
                  "hasCurrentMedicalConditions",
                  e.target.value
                )
              }
            >
              <Radio value={true}>Có</Radio>
              <Radio value={false}>Không</Radio>
            </Radio.Group>
          </Form.Item>

          {healthSurvey.hasCurrentMedicalConditions === true && (
            <Form.Item label="Ghi rõ bệnh lý hiện tại" required>
              <Input.TextArea
                value={healthSurvey.currentMedicalConditionsDetail}
                onChange={(e) =>
                  onHealthSurveyChange(
                    "currentMedicalConditionsDetail",
                    e.target.value
                  )
                }
                placeholder="Mô tả chi tiết bệnh lý hiện tại..."
                rows={3}
              />
            </Form.Item>
          )}
        </div>

        <Divider />

        {/* Question 3 */}
        <div id="question-3">
          <Title level={5}>
            3. Anh/chị có tiền sử mắc các bệnh nghiêm trọng sau không?
          </Title>
          <Text type="secondary" className="question-subtitle">
            Ung thư, bệnh tim mạch, đái tháo đường, tăng huyết áp, hen suyễn,
            động kinh, bệnh thận, bệnh gan, bệnh phổi mãn tính, bệnh tự miễn
          </Text>
          <Form.Item>
            <Radio.Group
              value={healthSurvey.hasPreviousSeriousConditions}
              onChange={(e) =>
                onHealthSurveyChange(
                  "hasPreviousSeriousConditions",
                  e.target.value
                )
              }
            >
              <Radio value={true}>Có</Radio>
              <Radio value={false}>Không</Radio>
              <Radio value="other">Bệnh khác</Radio>
            </Radio.Group>
          </Form.Item>

          {healthSurvey.hasPreviousSeriousConditions === "other" && (
            <Form.Item label="Mô tả bệnh nghiêm trọng khác" required>
              <Input.TextArea
                value={healthSurvey.otherPreviousConditions}
                onChange={(e) =>
                  onHealthSurveyChange(
                    "otherPreviousConditions",
                    e.target.value
                  )
                }
                placeholder="Mô tả chi tiết bệnh nghiêm trọng khác..."
                rows={3}
              />
            </Form.Item>
          )}
        </div>

        <Divider />

        {/* Question 4 */}
        <div id="question-4">
          <Title level={5}>
            4. Trong 12 tháng qua, anh/chị có gặp phải tình huống nào sau đây không?
          </Title>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.hadMalariaSyphilisTuberculosis}
              onChange={(e) =>
                onCheckboxChange(
                  "hadMalariaSyphilisTuberculosis",
                  e.target.checked,
                  "last12MonthsNone",
                  ["hadBloodTransfusion", "hadVaccination"]
                )
              }
            >
              Sốt rét, giang mai, lao phổi
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.hadBloodTransfusion}
              onChange={(e) =>
                onCheckboxChange(
                  "hadBloodTransfusion",
                  e.target.checked,
                  "last12MonthsNone",
                  ["hadMalariaSyphilisTuberculosis", "hadVaccination"]
                )
              }
            >
              Truyền máu
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.hadVaccination}
              onChange={(e) =>
                onCheckboxChange(
                  "hadVaccination",
                  e.target.checked,
                  "last12MonthsNone",
                  ["hadMalariaSyphilisTuberculosis", "hadBloodTransfusion"]
                )
              }
            >
              Tiêm chủng vaccine
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.last12MonthsNone}
              onChange={(e) =>
                onCheckboxChange(
                  "last12MonthsNone",
                  e.target.checked,
                  "last12MonthsNone",
                  ["hadMalariaSyphilisTuberculosis", "hadBloodTransfusion", "hadVaccination"]
                )
              }
            >
              Không
            </Checkbox>
          </Form.Item>
        </div>

        <Divider />

        {/* Question 5 */}
        <div id="question-5">
          <Title level={5}>
            5. Trong 6 tháng qua, anh/chị có gặp phải tình huống nào sau đây không?
          </Title>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.hadTyphoidSepsis}
              onChange={(e) =>
                onCheckboxChange(
                  "hadTyphoidSepsis",
                  e.target.checked,
                  "last6MonthsNone",
                  ["unexplainedWeightLoss", "persistentLymphNodes", "invasiveMedicalProcedures", "tattoosPiercings", "drugUse", "bloodExposure", "livedWithHepatitisB", "sexualContactWithInfected", "sameSexContact"]
                )
              }
            >
              Thương hàn, nhiễm trùng huyết
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.unexplainedWeightLoss}
              onChange={(e) =>
                onCheckboxChange(
                  "unexplainedWeightLoss",
                  e.target.checked,
                  "last6MonthsNone",
                  ["hadTyphoidSepsis", "persistentLymphNodes", "invasiveMedicalProcedures", "tattoosPiercings", "drugUse", "bloodExposure", "livedWithHepatitisB", "sexualContactWithInfected", "sameSexContact"]
                )
              }
            >
              Sụt cân không rõ nguyên nhân
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.persistentLymphNodes}
              onChange={(e) =>
                onCheckboxChange(
                  "persistentLymphNodes",
                  e.target.checked,
                  "last6MonthsNone",
                  ["hadTyphoidSepsis", "unexplainedWeightLoss", "invasiveMedicalProcedures", "tattoosPiercings", "drugUse", "bloodExposure", "livedWithHepatitisB", "sexualContactWithInfected", "sameSexContact"]
                )
              }
            >
              Hạch bạch huyết to kéo dài
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.invasiveMedicalProcedures}
              onChange={(e) =>
                onCheckboxChange(
                  "invasiveMedicalProcedures",
                  e.target.checked,
                  "last6MonthsNone",
                  ["hadTyphoidSepsis", "unexplainedWeightLoss", "persistentLymphNodes", "tattoosPiercings", "drugUse", "bloodExposure", "livedWithHepatitisB", "sexualContactWithInfected", "sameSexContact"]
                )
              }
            >
              Phẫu thuật, nội soi có xâm lấn
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.tattoosPiercings}
              onChange={(e) =>
                onCheckboxChange(
                  "tattoosPiercings",
                  e.target.checked,
                  "last6MonthsNone",
                  ["hadTyphoidSepsis", "unexplainedWeightLoss", "persistentLymphNodes", "invasiveMedicalProcedures", "drugUse", "bloodExposure", "livedWithHepatitisB", "sexualContactWithInfected", "sameSexContact"]
                )
              }
            >
              Xăm hình, xỏ khuyên
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.drugUse}
              onChange={(e) =>
                onCheckboxChange(
                  "drugUse",
                  e.target.checked,
                  "last6MonthsNone",
                  ["hadTyphoidSepsis", "unexplainedWeightLoss", "persistentLymphNodes", "invasiveMedicalProcedures", "tattoosPiercings", "bloodExposure", "livedWithHepatitisB", "sexualContactWithInfected", "sameSexContact"]
                )
              }
            >
              Sử dụng ma túy
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.bloodExposure}
              onChange={(e) =>
                onCheckboxChange(
                  "bloodExposure",
                  e.target.checked,
                  "last6MonthsNone",
                  ["hadTyphoidSepsis", "unexplainedWeightLoss", "persistentLymphNodes", "invasiveMedicalProcedures", "tattoosPiercings", "drugUse", "livedWithHepatitisB", "sexualContactWithInfected", "sameSexContact"]
                )
              }
            >
              Tiếp xúc với máu người khác
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.livedWithHepatitisB}
              onChange={(e) =>
                onCheckboxChange(
                  "livedWithHepatitisB",
                  e.target.checked,
                  "last6MonthsNone",
                  ["hadTyphoidSepsis", "unexplainedWeightLoss", "persistentLymphNodes", "invasiveMedicalProcedures", "tattoosPiercings", "drugUse", "bloodExposure", "sexualContactWithInfected", "sameSexContact"]
                )
              }
            >
              Sống chung với người bị viêm gan B
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.sexualContactWithInfected}
              onChange={(e) =>
                onCheckboxChange(
                  "sexualContactWithInfected",
                  e.target.checked,
                  "last6MonthsNone",
                  ["hadTyphoidSepsis", "unexplainedWeightLoss", "persistentLymphNodes", "invasiveMedicalProcedures", "tattoosPiercings", "drugUse", "bloodExposure", "livedWithHepatitisB", "sameSexContact"]
                )
              }
            >
              Quan hệ tình dục với người nhiễm HIV/AIDS
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.sameSexContact}
              onChange={(e) =>
                onCheckboxChange(
                  "sameSexContact",
                  e.target.checked,
                  "last6MonthsNone",
                  ["hadTyphoidSepsis", "unexplainedWeightLoss", "persistentLymphNodes", "invasiveMedicalProcedures", "tattoosPiercings", "drugUse", "bloodExposure", "livedWithHepatitisB", "sexualContactWithInfected"]
                )
              }
            >
              Quan hệ tình dục đồng giới
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.last6MonthsNone}
              onChange={(e) =>
                onCheckboxChange(
                  "last6MonthsNone",
                  e.target.checked,
                  "last6MonthsNone",
                  ["hadTyphoidSepsis", "unexplainedWeightLoss", "persistentLymphNodes", "invasiveMedicalProcedures", "tattoosPiercings", "drugUse", "bloodExposure", "livedWithHepatitisB", "sexualContactWithInfected", "sameSexContact"]
                )
              }
            >
              Không
            </Checkbox>
          </Form.Item>
        </div>

        <Divider />

        {/* Question 6 */}
        <div id="question-6">
          <Title level={5}>
            6. Trong 1 tháng qua, anh/chị có gặp phải tình huống nào sau đây không?
          </Title>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.hadUrinaryInfection}
              onChange={(e) =>
                onCheckboxChange(
                  "hadUrinaryInfection",
                  e.target.checked,
                  "last1MonthNone",
                  ["visitedEpidemicArea"]
                )
              }
            >
              Nhiễm trùng đường tiết niệu
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.visitedEpidemicArea}
              onChange={(e) =>
                onCheckboxChange(
                  "visitedEpidemicArea",
                  e.target.checked,
                  "last1MonthNone",
                  ["hadUrinaryInfection"]
                )
              }
            >
              Đến vùng dịch tễ
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.last1MonthNone}
              onChange={(e) =>
                onCheckboxChange(
                  "last1MonthNone",
                  e.target.checked,
                  "last1MonthNone",
                  ["hadUrinaryInfection", "visitedEpidemicArea"]
                )
              }
            >
              Không
            </Checkbox>
          </Form.Item>
        </div>

        <Divider />

        {/* Question 7 */}
        <div id="question-7">
          <Title level={5}>
            7. Trong 14 ngày qua, anh/chị có triệu chứng nào sau đây không?
          </Title>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.hadFluSymptoms}
              onChange={(e) =>
                onCheckboxChange(
                  "hadFluSymptoms",
                  e.target.checked,
                  "last14DaysNone",
                  ["hasOtherSymptoms"]
                )
              }
            >
              Sốt, ho, khó thở, mất vị giác/khứu giác
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={!!healthSurvey.otherSymptoms}
              onChange={(e) => {
                if (e.target.checked) {
                  onHealthSurveyChange("otherSymptoms", " ");
                  onHealthSurveyChange("last14DaysNone", false);
                } else {
                  onHealthSurveyChange("otherSymptoms", "");
                }
              }}
            >
              Khác (cụ thể):
            </Checkbox>
            {!!healthSurvey.otherSymptoms && (
              <Input
                value={healthSurvey.otherSymptoms}
                onChange={(e) =>
                  onHealthSurveyChange("otherSymptoms", e.target.value)
                }
                placeholder="Mô tả triệu chứng khác..."
                style={{ marginTop: 8 }}
              />
            )}
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.last14DaysNone}
              onChange={(e) =>
                onCheckboxChange(
                  "last14DaysNone",
                  e.target.checked,
                  "last14DaysNone",
                  ["hadFluSymptoms"]
                )
              }
            >
              Không
            </Checkbox>
          </Form.Item>
        </div>

        <Divider />

        {/* Question 8 */}
        <div id="question-8">
          <Title level={5}>
            8. Trong 7 ngày qua, anh/chị có sử dụng thuốc nào sau đây không?
          </Title>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.tookAntibiotics}
              onChange={(e) =>
                onCheckboxChange(
                  "tookAntibiotics",
                  e.target.checked,
                  "last7DaysNone",
                  ["hasOtherMedications"]
                )
              }
            >
              Kháng sinh
            </Checkbox>
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={!!healthSurvey.otherMedications}
              onChange={(e) => {
                if (e.target.checked) {
                  onHealthSurveyChange("otherMedications", " ");
                  onHealthSurveyChange("last7DaysNone", false);
                } else {
                  onHealthSurveyChange("otherMedications", "");
                }
              }}
            >
              Khác (cụ thể):
            </Checkbox>
            {!!healthSurvey.otherMedications && (
              <Input
                value={healthSurvey.otherMedications}
                onChange={(e) =>
                  onHealthSurveyChange("otherMedications", e.target.value)
                }
                placeholder="Mô tả thuốc khác..."
                style={{ marginTop: 8 }}
              />
            )}
          </Form.Item>
          <Form.Item>
            <Checkbox
              checked={healthSurvey.last7DaysNone}
              onChange={(e) =>
                onCheckboxChange(
                  "last7DaysNone",
                  e.target.checked,
                  "last7DaysNone",
                  ["tookAntibiotics"]
                )
              }
            >
              Không
            </Checkbox>
          </Form.Item>
        </div>

        <Divider />

        {/* Question 9 - Women Only */}
        {personalInfo.gender === "female" && (
          <>
            <div id="question-9">
              <Title level={5}>
                9. Dành cho phụ nữ: Anh/chị có tình trạng nào sau đây không?
              </Title>
              <Form.Item>
                <Checkbox
                  checked={healthSurvey.isPregnantOrNursing}
                  onChange={(e) =>
                    onCheckboxChange(
                      "isPregnantOrNursing",
                      e.target.checked,
                      "womenQuestionsNone",
                      ["hadPregnancyTermination"]
                    )
                  }
                >
                  Đang mang thai hoặc cho con bú
                </Checkbox>
              </Form.Item>
              <Form.Item>
                <Checkbox
                  checked={healthSurvey.hadPregnancyTermination}
                  onChange={(e) =>
                    onCheckboxChange(
                      "hadPregnancyTermination",
                      e.target.checked,
                      "womenQuestionsNone",
                      ["isPregnantOrNursing"]
                    )
                  }
                >
                  Đã phá thai trong 6 tháng qua
                </Checkbox>
              </Form.Item>
              <Form.Item>
                <Checkbox
                  checked={healthSurvey.womenQuestionsNone}
                  onChange={(e) =>
                    onCheckboxChange(
                      "womenQuestionsNone",
                      e.target.checked,
                      "womenQuestionsNone",
                      ["isPregnantOrNursing", "hadPregnancyTermination"]
                    )
                  }
                >
                  Không
                </Checkbox>
              </Form.Item>
            </div>
            <Divider />
          </>
        )}

        {/* Navigation Buttons */}
        <div className="form-navigation">
          <Button
            size="large"
            onClick={onBack}
            icon={<ArrowLeftOutlined />}
            className="back-button"
          >
            Quay lại
          </Button>
          <Button
            type="primary"
            size="large"
            loading={loading}
            onClick={onSubmit}
            className="submit-button"
          >
            {loading ? "Đang xử lý..." : "Tiếp tục"}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default HealthSurveyStep;
