import * as XLSX from 'xlsx';

/**
 * Service for exporting data to Excel files
 */
const excelExportService = {
  /**
   * Export manager dashboard data to Excel
   * @param {Object} dashboardData - Dashboard data to export
   * @returns {void}
   */
  exportManagerDashboard: (dashboardData) => {
    try {
      // Create a new workbook
      const workbook = XLSX.utils.book_new();

      // 1. Overview Sheet
      const overviewData = [
        ['BÁO CÁO TỔNG QUAN HỆ THỐNG QUẢN LÝ HIẾN MÁU'],
        ['Ngày xuất:', new Date().toLocaleDateString('vi-VN')],
        ['Thời gian:', new Date().toLocaleTimeString('vi-VN')],
        [],
        ['THỐNG KÊ TỔNG QUAN'],
        ['Tổng số đơn vị máu:', dashboardData.totalBloodUnits || 0],
        ['Tổng số người hiến:', dashboardData.totalDonors || 0],
        ['Tổng số yêu cầu máu:', dashboardData.totalRequests || 0],
        ['Số người dùng hoạt động:', dashboardData.activeUsers || 0],
        [],
        ['THỐNG KÊ YÊU CẦU MÁU'],
        ['Yêu cầu đang chờ:', dashboardData.pendingRequests || 0],
        ['Yêu cầu đã hoàn thành:', dashboardData.completedRequests || 0],
        ['Yêu cầu bị từ chối:', dashboardData.rejectedRequests || 0],
      ];

      const overviewSheet = XLSX.utils.aoa_to_sheet(overviewData);
      XLSX.utils.book_append_sheet(workbook, overviewSheet, 'Tổng quan');

      // 2. Blood Inventory Sheet
      if (dashboardData.bloodInventory && dashboardData.bloodInventory.length > 0) {
        const inventoryHeaders = [
          'ID Kho',
          'Nhóm máu',
          'Rh',
          'Loại thành phần',
          'Số lượng',
          'Trạng thái',
          'Cập nhật lần cuối'
        ];

        const inventoryData = dashboardData.bloodInventory.map(item => [
          item.inventoryID || item.inventoryId || '',
          item.bloodGroup || '',
          item.rhType || '',
          item.componentType || '',
          item.quantity || 0,
          item.status || '',
          item.lastUpdated ? new Date(item.lastUpdated).toLocaleDateString('vi-VN') : ''
        ]);

        const inventorySheet = XLSX.utils.aoa_to_sheet([inventoryHeaders, ...inventoryData]);
        XLSX.utils.book_append_sheet(workbook, inventorySheet, 'Kho máu');
      }

      // 3. Blood Group Distribution Sheet
      if (dashboardData.bloodGroupData && dashboardData.bloodGroupData.length > 0) {
        const bloodGroupHeaders = ['Nhóm máu', 'Số lượng', 'Tỷ lệ %'];
        const totalUnits = dashboardData.bloodGroupData.reduce((sum, item) => sum + (item.value || 0), 0);
        
        const bloodGroupData = dashboardData.bloodGroupData.map(item => [
          item.name || '',
          item.value || 0,
          totalUnits > 0 ? ((item.value / totalUnits) * 100).toFixed(2) + '%' : '0%'
        ]);

        const bloodGroupSheet = XLSX.utils.aoa_to_sheet([bloodGroupHeaders, ...bloodGroupData]);
        XLSX.utils.book_append_sheet(workbook, bloodGroupSheet, 'Phân bố nhóm máu');
      }

      // 4. Recent Requests Sheet
      if (dashboardData.recentRequests && dashboardData.recentRequests.length > 0) {
        const requestHeaders = [
          'ID Yêu cầu',
          'Tên bệnh nhân',
          'Nhóm máu',
          'Rh',
          'Số lượng',
          'Trạng thái',
          'Ngày tạo',
          'Bác sĩ',
          'Cơ sở y tế'
        ];

        const requestData = dashboardData.recentRequests.slice(0, 100).map(request => [
          request.requestId || request.requestID || '',
          request.patientName || '',
          request.bloodGroup || '',
          request.rhType || '',
          request.quantity || 0,
          excelExportService.getStatusText(request.status),
          request.createdTime ? new Date(request.createdTime).toLocaleDateString('vi-VN') : '',
          request.doctorName || '',
          request.facilityName || ''
        ]);

        const requestSheet = XLSX.utils.aoa_to_sheet([requestHeaders, ...requestData]);
        XLSX.utils.book_append_sheet(workbook, requestSheet, 'Yêu cầu gần đây');
      }

      // 5. Critical Inventory Sheet
      if (dashboardData.criticalInventory && dashboardData.criticalInventory.length > 0) {
        const criticalHeaders = [
          'Nhóm máu',
          'Loại thành phần',
          'Số lượng',
          'Mức độ cảnh báo',
          'Ghi chú'
        ];

        const criticalData = dashboardData.criticalInventory.map(item => [
          `${item.bloodGroup}${item.rhType}`,
          item.componentType || '',
          item.quantity || 0,
          item.quantity <= 2 ? 'Cực kỳ thiếu' : 'Thiếu',
          item.quantity <= 2 ? 'Cần bổ sung ngay lập tức' : 'Cần bổ sung'
        ]);

        const criticalSheet = XLSX.utils.aoa_to_sheet([criticalHeaders, ...criticalData]);
        XLSX.utils.book_append_sheet(workbook, criticalSheet, 'Kho máu thiếu');
      }

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `BaoCao_QuanLy_HienMau_${timestamp}.xlsx`;

      // Write and download the file
      XLSX.writeFile(workbook, filename);

      return {
        success: true,
        message: 'Xuất báo cáo Excel thành công',
        filename
      };
    } catch (error) {
      console.error('Error exporting Excel:', error);
      return {
        success: false,
        error: error.message || 'Có lỗi xảy ra khi xuất báo cáo Excel'
      };
    }
  },

  /**
   * Get status text in Vietnamese
   * @param {number} status - Status code
   * @returns {string} Status text
   */
  getStatusText: (status) => {
    const statusMap = {
      0: 'Chờ duyệt',
      1: 'Đã chấp nhận',
      2: 'Hoàn thành',
      3: 'Từ chối',
      4: 'Đã xóa'
    };
    return statusMap[status] || 'Không xác định';
  },

  /**
   * Export blood inventory data to Excel
   * @param {Array} inventoryData - Blood inventory data
   * @returns {Object} Export result
   */
  exportBloodInventory: (inventoryData) => {
    try {
      const workbook = XLSX.utils.book_new();

      const headers = [
        'ID Kho',
        'Nhóm máu',
        'Rh',
        'Loại thành phần',
        'Số lượng',
        'Trạng thái',
        'Hiếm',
        'Cập nhật lần cuối'
      ];

      const data = inventoryData.map(item => [
        item.inventoryID || item.inventoryId || '',
        item.bloodGroup || '',
        item.rhType || '',
        item.componentType || '',
        item.quantity || 0,
        item.status || '',
        item.isRare ? 'Có' : 'Không',
        item.lastUpdated ? new Date(item.lastUpdated).toLocaleDateString('vi-VN') : ''
      ]);

      const worksheet = XLSX.utils.aoa_to_sheet([headers, ...data]);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Kho máu');

      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `KhoMau_${timestamp}.xlsx`;

      XLSX.writeFile(workbook, filename);

      return {
        success: true,
        message: 'Xuất dữ liệu kho máu thành công',
        filename
      };
    } catch (error) {
      console.error('Error exporting blood inventory:', error);
      return {
        success: false,
        error: error.message || 'Có lỗi xảy ra khi xuất dữ liệu kho máu'
      };
    }
  },

  /**
   * Export blood requests data to Excel
   * @param {Array} requestsData - Blood requests data
   * @returns {Object} Export result
   */
  exportBloodRequests: (requestsData) => {
    try {
      const workbook = XLSX.utils.book_new();

      const headers = [
        'ID Yêu cầu',
        'Tên bệnh nhân',
        'Tuổi',
        'Giới tính',
        'Nhóm máu',
        'Rh',
        'Số lượng',
        'Lý do',
        'Trạng thái',
        'Ngày tạo',
        'Bác sĩ',
        'Số điện thoại',
        'Cơ sở y tế'
      ];

      const data = requestsData.map(request => [
        request.requestId || request.requestID || '',
        request.patientName || '',
        request.age || '',
        request.gender || '',
        request.bloodGroup || '',
        request.rhType || '',
        request.quantity || 0,
        request.reason || '',
        excelExportService.getStatusText(request.status),
        request.createdTime ? new Date(request.createdTime).toLocaleDateString('vi-VN') : '',
        request.doctorName || '',
        request.doctorPhone || '',
        request.facilityName || ''
      ]);

      const worksheet = XLSX.utils.aoa_to_sheet([headers, ...data]);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Yêu cầu máu');

      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `YeuCauMau_${timestamp}.xlsx`;

      XLSX.writeFile(workbook, filename);

      return {
        success: true,
        message: 'Xuất dữ liệu yêu cầu máu thành công',
        filename
      };
    } catch (error) {
      console.error('Error exporting blood requests:', error);
      return {
        success: false,
        error: error.message || 'Có lỗi xảy ra khi xuất dữ liệu yêu cầu máu'
      };
    }
  }
};

export default excelExportService;
