import React, { useState, useEffect } from "react";
import axios from "axios";
import NominatimService from "../../services/nominatimService";
import GeolibService from "../../services/geolibService";
import { useDistanceUpdate } from "../../hooks/useDistanceUpdate";
// import vietnamProvincesData from "../../data/vietnam-provinces.json"; // Now using local JSON file via vietnamAddressService
import "../../styles/components/AddressForm.scss";

const AddressForm = ({ onAddressChange, initialAddress, disabled = false }) => {
  const [addressData, setAddressData] = useState({
    houseNumber: "",
    street: "",
    province: "",
    ward: "",
    provinceName: "",
    wardName: "",
    fullAddress: "",
  });

  const [geocoding, setGeocoding] = useState(false);
  const [error, setError] = useState("");
  const [distance, setDistance] = useState(null);
  const [coordinates, setCoordinates] = useState({ lat: null, lng: null });

  // Hook for updating distance to database
  const {
    updateDistance,
    isUpdating: isUpdatingDistance,
    updateError: distanceUpdateError,
  } = useDistanceUpdate();

  // Helper function to save distance to database
  const saveDistanceToDatabase = async (calculatedDistance) => {
    try {
      const result = await updateDistance(calculatedDistance);
      if (!result.success) {
        // Silent fail - distance is not critical for form submission
      }
    } catch (error) {
      // Silent fail - distance is not critical for form submission
    }
  };

  // Initialize with initial address if provided
  useEffect(() => {
    if (initialAddress) {
      if (typeof initialAddress === "string") {
        setAddressData((prev) => ({ ...prev, fullAddress: initialAddress }));
      } else if (typeof initialAddress === "object") {
        setAddressData((prev) => ({ ...prev, ...initialAddress }));
      }
    }
  }, [initialAddress]);

  // Update full address when individual fields change (updated for province + ward only)
  useEffect(() => {
    const { houseNumber, wardName, provinceName } = addressData;
    const parts = [houseNumber, wardName, provinceName].filter(
      (part) => part && part.trim()
    );
    const fullAddress = parts.join(", ");

    if (fullAddress !== addressData.fullAddress && parts.length > 0) {
      setAddressData((prev) => ({ ...prev, fullAddress }));
    }
  }, [addressData.houseNumber, addressData.wardName, addressData.provinceName]);

  // Geocode when full address changes
  useEffect(() => {
    if (addressData.fullAddress && addressData.fullAddress.length > 15) {
      const timer = setTimeout(() => {
        geocodeAddress(addressData.fullAddress);
      }, 1500); // Debounce 1.5 seconds

      return () => clearTimeout(timer);
    }
  }, [addressData.fullAddress]);

  const geocodeAddress = async (fullAddress) => {
    if (!fullAddress || fullAddress.length < 10) return;

    setGeocoding(true);
    setError("");

    try {
      // Step 1: Geocode address using Nominatim
      let geocodeResult;
      try {
        geocodeResult = await NominatimService.geocodeAddress(fullAddress);
      } catch (nominatimError) {
        // Fallback: try alternative geocoding
        geocodeResult = await tryAlternativeGeocoding(fullAddress);

        if (!geocodeResult) {
          throw new Error(
            "Không thể tìm thấy địa chỉ này. Vui lòng kiểm tra lại thông tin địa chỉ."
          );
        }
      }

      setCoordinates({
        lat: geocodeResult.lat,
        lng: geocodeResult.lng,
      });

      // Step 2: Calculate distance using Geolib
      const calculatedDistance = GeolibService.getDistanceToHospital({
        lat: geocodeResult.lat,
        lng: geocodeResult.lng,
      });

      setDistance(calculatedDistance);

      // Save distance to database
      await saveDistanceToDatabase(calculatedDistance);

      // No route calculation needed - just use distance

      // Notify parent component
      if (onAddressChange) {
        onAddressChange({
          ...addressData,
          coordinates: {
            lat: geocodeResult.lat,
            lng: geocodeResult.lng,
          },
          distance: calculatedDistance,
          formattedAddress: geocodeResult.address || fullAddress,
          priority: GeolibService.getDistancePriority(calculatedDistance),
        });
      }
    } catch (error) {
      console.error("Geocoding error:", error);
      setError(error.message || "Không thể xác định vị trí địa chỉ này");

      // Final fallback: try basic keyword-based estimation
      try {
        const fallbackResult = await tryKeywordBasedEstimation(fullAddress);
        if (fallbackResult) {
          setCoordinates(fallbackResult.coordinates);
          setDistance(fallbackResult.distance);

          // Save fallback distance to database
          await saveDistanceToDatabase(fallbackResult.distance);

          if (onAddressChange) {
            onAddressChange({
              ...addressData,
              ...fallbackResult,
              formattedAddress: fullAddress,
            });
          }

          // Clear error if fallback works
          setError("");
        }
      } catch (fallbackError) {
        console.error("Fallback estimation failed:", fallbackError);
      }
    } finally {
      setGeocoding(false);
    }
  };

  // Alternative geocoding using different approach
  const tryAlternativeGeocoding = async (address) => {
    try {
      // Try with simplified search (just city and district)
      const addressParts = address.split(",").map((part) => part.trim());
      const simplifiedAddress = addressParts.slice(-2).join(", "); // Last 2 parts (district, city)

      console.log("Trying simplified address:", simplifiedAddress);

      const searchParams = new URLSearchParams({
        q: simplifiedAddress,
        format: "json",
        countrycodes: "vn",
        limit: 1,
        addressdetails: 1,
      });

      const url = `https://nominatim.openstreetmap.org/search?${searchParams.toString()}`;

      const response = await fetch(url, {
        headers: {
          "User-Agent": "BloodDonationApp/1.0",
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.length > 0) {
          const result = data[0];
          console.log("Alternative geocoding success:", result);

          return {
            lat: parseFloat(result.lat),
            lng: parseFloat(result.lon),
            address: result.display_name,
          };
        }
      }
    } catch (error) {
      console.warn("Alternative geocoding failed:", error);
    }

    return null;
  };

  const tryKeywordBasedEstimation = async (address) => {
    const addressLower = address.toLowerCase();
    let mockLat, mockLng, estimatedDistance;

    // Major cities with known coordinates
    if (addressLower.includes("hà nội") || addressLower.includes("hanoi")) {
      mockLat = 21.0285 + (Math.random() - 0.5) * 0.1;
      mockLng = 105.8542 + (Math.random() - 0.5) * 0.1;
      estimatedDistance = 1100 + Math.random() * 100;
    } else if (
      addressLower.includes("đà nẵng") ||
      addressLower.includes("da nang")
    ) {
      mockLat = 16.0471 + (Math.random() - 0.5) * 0.1;
      mockLng = 108.2068 + (Math.random() - 0.5) * 0.1;
      estimatedDistance = 600 + Math.random() * 50;
    } else if (
      addressLower.includes("cần thơ") ||
      addressLower.includes("can tho")
    ) {
      mockLat = 10.0452 + (Math.random() - 0.5) * 0.1;
      mockLng = 105.7469 + (Math.random() - 0.5) * 0.1;
      estimatedDistance = 170 + Math.random() * 20;
    } else if (
      addressLower.includes("vũng tàu") ||
      addressLower.includes("vung tau")
    ) {
      mockLat = 10.4113 + (Math.random() - 0.5) * 0.1;
      mockLng = 107.1365 + (Math.random() - 0.5) * 0.1;
      estimatedDistance = 125 + Math.random() * 15;
    } else if (addressLower.includes("nha trang")) {
      mockLat = 12.2388 + (Math.random() - 0.5) * 0.1;
      mockLng = 109.1967 + (Math.random() - 0.5) * 0.1;
      estimatedDistance = 450 + Math.random() * 50;
    } else if (addressLower.includes("huế") || addressLower.includes("hue")) {
      mockLat = 16.4637 + (Math.random() - 0.5) * 0.1;
      mockLng = 107.5909 + (Math.random() - 0.5) * 0.1;
      estimatedDistance = 700 + Math.random() * 50;
    } else if (
      addressLower.includes("quận 1") ||
      addressLower.includes("district 1")
    ) {
      mockLat = 10.7769 + (Math.random() - 0.5) * 0.01;
      mockLng = 106.7009 + (Math.random() - 0.5) * 0.01;
      estimatedDistance = 2 + Math.random() * 3;
    } else if (
      addressLower.includes("quận 7") ||
      addressLower.includes("district 7")
    ) {
      mockLat = 10.7379 + (Math.random() - 0.5) * 0.02;
      mockLng = 106.7218 + (Math.random() - 0.5) * 0.02;
      estimatedDistance = 15 + Math.random() * 10;
    } else if (
      addressLower.includes("tp.hcm") ||
      addressLower.includes("ho chi minh") ||
      addressLower.includes("sài gòn") ||
      addressLower.includes("saigon")
    ) {
      // General HCM area
      mockLat = 10.7751237 + (Math.random() - 0.5) * 0.1;
      mockLng = 106.6862143 + (Math.random() - 0.5) * 0.1;
      estimatedDistance = 5 + Math.random() * 25;
    } else {
      // Unknown location - estimate based on Vietnam bounds
      mockLat = 8.5 + Math.random() * 15; // Random lat in Vietnam
      mockLng = 102 + Math.random() * 8; // Random lng in Vietnam

      // Calculate distance using Geolib
      estimatedDistance = GeolibService.getDistanceToHospital({
        lat: mockLat,
        lng: mockLng,
      });
    }

    return {
      coordinates: { lat: mockLat, lng: mockLng },
      distance: Math.round(estimatedDistance * 100) / 100,
      priority: GeolibService.getDistancePriority(estimatedDistance),
    };
  };

  const handleFieldChange = (field, value) => {
    // Handle dropdown changes with name mapping for local JSON data
    if (field === "province") {
      const selectedProvince = provinceList.find((p) => p === value);
      setAddressData((prev) => ({
        ...prev,
        [field]: value,
        provinceName: selectedProvince || "",
        // Reset ward when province changes
        ward: "",
        wardName: "",
      }));
    } else if (field === "ward") {
      const selectedWard = wardList.find((w) => w === value);
      setAddressData((prev) => ({
        ...prev,
        [field]: value,
        wardName: selectedWard || "",
      }));
    } else {
      setAddressData((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const getDistanceColor = () => {
    if (!distance) return "#6c757d";
    if (distance <= 5) return "#28a745";
    if (distance <= 10) return "#ffc107";
    if (distance <= 20) return "#fd7e14";
    return "#dc3545";
  };

  const getDistanceText = () => {
    if (!distance) return "Chưa xác định";
    if (distance <= 5) return "Rất gần";
    if (distance <= 10) return "Gần";
    if (distance <= 20) return "Trung bình";
    if (distance <= 50) return "Xa";
    return "Rất xa";
  };

  return (
    <div className="address-form">
      <div className="address-fields">
        <div className="full-address-preview">
          <label>Địa chỉ đầy đủ:</label>
          <div className={`address-display ${geocoding ? "geocoding" : ""}`}>
            {addressData.fullAddress ||
              "Vui lòng điền đầy đủ thông tin địa chỉ"}
            {geocoding && (
              <span className="geocoding-indicator">
                <span className="loading-spinner"></span>
                Đang xác định vị trí...
              </span>
            )}
          </div>

          {addressData.fullAddress && addressData.fullAddress.length > 10 && (
            <div className="address-help">
              <small>
                💡 Hệ thống sẽ tự động tính khoảng cách khi bạn điền đầy đủ
                thông tin địa chỉ
              </small>
            </div>
          )}
        </div>

        {error && (
          <div className="error-message">
            ⚠️ {error}
            <div className="error-help">
              <small>
                Gợi ý: Hãy đảm bảo bạn đã chọn đúng tỉnh/thành phố và phường/xã
              </small>
            </div>
          </div>
        )}
      </div>

      {/* Distance Information */}
      {distance && (
        <div className="distance-info-section">
          <h4> Thông tin khoảng cách</h4>

          <div className="distance-details">
            <div className="distance-item">
              <span className="distance-label">Khoảng cách đến bệnh viện:</span>
              <span
                className="distance-value"
                style={{ color: getDistanceColor() }}
              >
                {GeolibService.formatDistance(distance)} ({getDistanceText()})
              </span>
            </div>
          </div>

          <div className="hospital-info">
            <h5>🏥 Địa điểm hiến máu</h5>
            <div className="hospital-details">
              <div className="hospital-item">
                <strong>Bệnh viện Đa khoa Ánh Dương</strong>
              </div>
              <div className="hospital-item">
                Đường Cách Mạng Tháng 8, Quận 3, TP.HCM, Vietnam
              </div>
            </div>

            {coordinates.lat && coordinates.lng && (
              <div className="directions-links">
                <a
                  href={GeolibService.getDirectionsUrl(coordinates)}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="directions-link osm-link"
                >
                  Xem đường đi trên OpenStreetMap
                </a>
                <a
                  href={GeolibService.getDirectionsUrl(coordinates)}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="directions-link google-link"
                >
                  Xem trên Google Maps
                </a>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AddressForm;
