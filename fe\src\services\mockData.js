// Mock data for Blood Management System based on database schema
// NOTE: Most data has been replaced by real APIs. This file is kept for:
// - Development/testing scenarios
// - Constants and enums still in use
// - Backward compatibility

// User roles matching database Roles table
export const ROLES = {
  GUEST: "Guest",
  MEMBER: "1",
  STAFF_DOCTOR: "2",
  STAFF_BLOOD_MANAGER: "3",
  ADMIN: "4",
};

// User status matching database Users table
export const USER_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  BANNED: 2,
};

// Blood groups
export const BLOOD_GROUPS = {
  A: "A",
  B: "B",
  AB: "AB",
  O: "O",
};

// Rh types
export const RH_TYPES = {
  POSITIVE: "Rh+",
  NEGATIVE: "Rh-",
};

// Blood request status matching API specification
export const REQUEST_STATUS = {
  PENDING: 0, // Đang chờ xử lý
  ACCEPTED: 1, // Chấp nhận
  COMPLETED: 2, // <PERSON><PERSON><PERSON> thành
  REJECTED: 3, // Từ chối
};

// Blood request urgency levels
export const URGENCY_LEVELS = {
  NORMAL: 0,
  URGENT: 1,
  CRITICAL: 2,
};

// NOTE: Blood component types moved to bloodInventoryConstants.js
// Import from bloodInventoryConstants.js instead

// Doctor types for different departments
export const DOCTOR_TYPES = {
  BLOOD_DEPARTMENT: "blood_department",
  OTHER_DEPARTMENT: "other_department",
};

// Department IDs mapping
export const DEPARTMENT_IDS = {
  "Khoa Huyết học": 1,
  "Khoa Tim mạch": 2,
  "Khoa Nhi": 3,
  "Khoa Cấp Cứu": 4,
  "Khoa Giải phẫu": 5,
  "Khoa Ngoại": 6,
};

// NOTE: Mock users data - mostly replaced by real APIs
// Still used in some development/testing scenarios
export const mockUsers = [
  // Members (can be both donors and recipients)
  {
    userID: 1,
    firebaseUID: "firebase_uid_001",
    email: "<EMAIL>",
    phone: "0123456789",
    password: "Member1@", // In real app, this would be hashed
    name: "Nguyễn Văn A",
    age: 34,
    gender: "Male",
    address: "Quận 1, TP.HCM",
    bloodGroup: BLOOD_GROUPS.O,
    rhType: RH_TYPES.POSITIVE,
    status: USER_STATUS.ACTIVE,
    roleID: 2, // Member role
    role: ROLES.MEMBER,
    department: null,
    createdAt: "2024-01-01T00:00:00Z",
    // Additional fields for frontend
    profile: {
      fullName: "Nguyễn Văn A",
      phone: "0123456789",
      email: "<EMAIL>",
      bloodType: "O+",
      address: "Quận 1, TP.HCM",
      dateOfBirth: "1990-01-01",
      gender: "male",
    },
    activityHistory: [
      {
        id: "act1",
        type: "donation",
        date: "2024-01-10",
        bloodType: "O+",
        quantity: 450,
        location: "Bệnh viện XYZ",
        status: "completed",
        notes: "Hiến máu thành công",
      },
    ],
  },
  {
    userID: 2,
    firebaseUID: "firebase_uid_002",
    email: "<EMAIL>",
    phone: "**********",
    password: "Member2@",
    name: "Trần Thị B",
    age: 32,
    gender: "Female",
    address: "Quận 2, TP.HCM",
    bloodGroup: BLOOD_GROUPS.A,
    rhType: RH_TYPES.POSITIVE,
    status: USER_STATUS.ACTIVE,
    roleID: 2,
    role: ROLES.MEMBER,
    department: null,
    createdAt: "2024-01-02T00:00:00Z",
    profile: {
      fullName: "Trần Thị B",
      phone: "**********",
      email: "<EMAIL>",
      bloodType: "A+",
      address: "Quận 2, TP.HCM",
      dateOfBirth: "1992-05-15",
      gender: "female",
    },
    activityHistory: [
      {
        id: "act2",
        type: "donation",
        date: "2024-01-08",
        bloodType: "A+",
        quantity: 450,
        location: "Bệnh viện XYZ",
        status: "medical_checked",
        notes: "Đã khám sàng lọc, chờ lịch hiến máu",
      },
    ],
  },
  {
    userID: 3,
    firebaseUID: "firebase_uid_003",
    email: "<EMAIL>",
    phone: "**********",
    password: "Member3@",
    name: "Lê Văn C",
    age: 36,
    gender: "Male",
    address: "Quận 3, TP.HCM",
    bloodGroup: BLOOD_GROUPS.B,
    rhType: RH_TYPES.POSITIVE,
    status: USER_STATUS.ACTIVE,
    roleID: 2,
    role: ROLES.MEMBER,
    department: null,
    createdAt: "2024-01-03T00:00:00Z",
    profile: {
      fullName: "Lê Văn C",
      phone: "**********",
      email: "<EMAIL>",
      bloodType: "B+",
      address: "Quận 3, TP.HCM",
      dateOfBirth: "1988-12-20",
      gender: "male",
    },
    activityHistory: [
      {
        id: "act3",
        type: "request",
        date: "2024-01-12",
        bloodType: "B+",
        quantity: 2,
        reason: "Phẫu thuật khẩn cấp",
        status: "approved",
        notes: "Yêu cầu đã được duyệt",
      },
    ],
  },
  {
    userID: 4,
    firebaseUID: "firebase_uid_004",
    email: "<EMAIL>",
    phone: "**********",
    password: "Doctor3@",
    name: "BS. Phạm Văn D",
    age: 40,
    gender: "Male",
    address: "Quận 4, TP.HCM",
    bloodGroup: BLOOD_GROUPS.AB,
    rhType: RH_TYPES.POSITIVE,
    status: USER_STATUS.ACTIVE,
    roleID: 3,
    role: ROLES.STAFF_DOCTOR,
    department: "Khoa Huyết học",
    doctorType: DOCTOR_TYPES.BLOOD_DEPARTMENT,
    createdAt: "2024-01-04T00:00:00Z",
    profile: {
      fullName: "BS. Phạm Văn D",
      phone: "**********",
      email: "<EMAIL>",
      department: "Khoa Huyết học",
      specialization: "Huyết học",
      licenseNumber: "BS003",
    },
  },
  {
    userID: 5,
    firebaseUID: "firebase_uid_005",
    email: "<EMAIL>",
    phone: "**********",
    password: "Manager2@",
    name: "Nguyễn Thị E",
    age: 35,
    gender: "Female",
    address: "Quận 5, TP.HCM",
    bloodGroup: BLOOD_GROUPS.A,
    rhType: RH_TYPES.NEGATIVE,
    status: USER_STATUS.ACTIVE,
    roleID: 4,
    role: ROLES.STAFF_BLOOD_MANAGER,
    department: "Quản lý Ngân hàng Máu",
    createdAt: "2024-01-05T00:00:00Z",
    profile: {
      fullName: "Nguyễn Thị E",
      phone: "**********",
      email: "<EMAIL>",
      department: "Quản lý Ngân hàng Máu",
      position: "Phó phòng",
    },
  },
  {
    id: "member4",
    email: "<EMAIL>",
    password: "Member4@",
    role: ROLES.MEMBER,
    profile: {
      fullName: "Phạm Thị D",
      phone: "**********",
      email: "<EMAIL>",
      bloodType: "AB+",
      address: "Quận 4, TP.HCM",
      dateOfBirth: "1995-03-10",
      gender: "female",
    },
    activityHistory: [
      {
        id: "act4",
        type: "donation",
        date: "2024-01-05",
        bloodType: "AB+",
        quantity: 450,
        location: "Bệnh viện XYZ",
        status: "completed",
        notes: "Hiến máu thành công, kết quả xét nghiệm tốt",
      },
      {
        id: "act5",
        type: "request",
        date: "2023-12-20",
        bloodType: "AB+",
        quantity: 1,
        reason: "Điều trị ung thư",
        status: "completed",
        notes: "Đã nhận máu thành công",
      },
    ],
  },
  {
    id: "member5",
    email: "<EMAIL>",
    password: "Member5@",
    role: ROLES.MEMBER,
    profile: {
      fullName: "Hoàng Văn E",
      phone: "0123456793",
      email: "<EMAIL>",
      bloodType: "O-",
      address: "Quận 5, TP.HCM",
      dateOfBirth: "1987-08-25",
      gender: "male",
    },
    activityHistory: [
      {
        id: "act6",
        type: "donation",
        date: "2024-01-10",
        bloodType: "O-",
        quantity: 450,
        location: "Bệnh viện XYZ",
        status: "completed",
        notes: "Hiến máu thành công, kết quả xét nghiệm tốt",
      },
      {
        id: "act7",
        type: "donation",
        date: "2023-10-15",
        bloodType: "O-",
        quantity: 450,
        location: "Bệnh viện XYZ",
        status: "completed",
        notes: "Hiến máu định kỳ",
      },
      {
        id: "act8",
        type: "request",
        date: "2023-08-20",
        bloodType: "O-",
        quantity: 1,
        reason: "Tai nạn giao thông khẩn cấp",
        status: "completed",
        notes: "Đã nhận máu kịp thời",
      },
    ],
  },

  // Staff-Doctor (Other Department)
  {
    userID: 6,
    firebaseUID: "firebase_uid_006",
    email: "<EMAIL>",
    phone: "**********",
    password: "Doctor1@",
    name: "BS. Nguyễn Văn H",
    age: 45,
    gender: "Male",
    address: "Quận 1, TP.HCM",
    bloodGroup: BLOOD_GROUPS.A,
    rhType: RH_TYPES.POSITIVE,
    status: USER_STATUS.ACTIVE,
    roleID: 3,
    role: ROLES.STAFF_DOCTOR,
    department: "Khoa Tim Mạch",
    doctorType: DOCTOR_TYPES.OTHER_DEPARTMENT,
    createdAt: "2024-01-06T00:00:00Z",
    profile: {
      fullName: "BS. Nguyễn Văn H",
      phone: "**********",
      email: "<EMAIL>",
      department: "Khoa Tim Mạch",
      specialization: "Tim mạch",
      licenseNumber: "BS001",
    },
  },
  // Staff-Doctor (Blood Department)
  {
    userID: 7,
    firebaseUID: "firebase_uid_007",
    email: "<EMAIL>",
    phone: "**********",
    password: "Doctor2@",
    name: "BS. Trần Thị I",
    age: 38,
    gender: "Female",
    address: "Quận 3, TP.HCM",
    bloodGroup: BLOOD_GROUPS.AB,
    rhType: RH_TYPES.NEGATIVE,
    status: USER_STATUS.ACTIVE,
    roleID: 3,
    role: ROLES.STAFF_DOCTOR,
    department: "Khoa Huyết học",
    doctorType: DOCTOR_TYPES.BLOOD_DEPARTMENT,
    createdAt: "2024-01-07T00:00:00Z",
    profile: {
      fullName: "BS. Trần Thị I",
      phone: "**********",
      email: "<EMAIL>",
      department: "Khoa Huyết học",
      specialization: "Huyết học",
      licenseNumber: "BS002",
    },
  },

  // Staff-BloodManager
  {
    userID: 8,
    firebaseUID: "firebase_uid_008",
    email: "<EMAIL>",
    phone: "**********",
    password: "Manager1@",
    name: "Lê Văn J",
    age: 42,
    gender: "Male",
    address: "Quận 1, TP.HCM",
    bloodGroup: BLOOD_GROUPS.O,
    rhType: RH_TYPES.NEGATIVE,
    status: USER_STATUS.ACTIVE,
    roleID: 4,
    role: ROLES.STAFF_BLOOD_MANAGER,
    department: "Quản lý Ngân hàng Máu",
    createdAt: "2024-01-08T00:00:00Z",
    profile: {
      fullName: "Lê Văn J",
      phone: "**********",
      email: "<EMAIL>",
      department: "Quản lý Ngân hàng Máu",
      position: "Trưởng phòng",
    },
  },
  // Admin Users
  {
    userID: 9,
    firebaseUID: "firebase_uid_009",
    email: "<EMAIL>",
    phone: "**********",
    password: "Admin123@",
    name: "Nguyễn Văn Admin",
    age: 44,
    gender: "Male",
    address: "Quận 1, TP.HCM",
    bloodGroup: BLOOD_GROUPS.A,
    rhType: RH_TYPES.POSITIVE,
    status: USER_STATUS.ACTIVE,
    roleID: 5,
    role: ROLES.ADMIN,
    department: "Quản trị hệ thống",
    createdAt: "2023-01-01T00:00:00Z",
    profile: {
      fullName: "Nguyễn Văn Admin",
      phone: "**********",
      email: "<EMAIL>",
      department: "Quản trị hệ thống",
      position: "Quản trị viên",
    },
  },
  {
    userID: 10,
    firebaseUID: "firebase_uid_010",
    email: "<EMAIL>",
    phone: "0123456800",
    password: "SuperAdmin1@",
    name: "Trần Thị Super Admin",
    age: 46,
    gender: "Female",
    address: "Quận 3, TP.HCM",
    bloodGroup: BLOOD_GROUPS.O,
    rhType: RH_TYPES.POSITIVE,
    status: USER_STATUS.ACTIVE,
    roleID: 5,
    role: ROLES.ADMIN,
    department: "Quản trị hệ thống",
    createdAt: "2023-01-01T00:00:00Z",
    profile: {
      fullName: "Trần Thị Super Admin",
      phone: "0123456800",
      email: "<EMAIL>",
      department: "Quản trị hệ thống",
      position: "Quản trị viên cấp cao",
    },
  },
].map((user) => {
  if (user.role === ROLES.STAFF_DOCTOR && user.department) {
    const deptName = user.department.trim();
    user.departmentID = DEPARTMENT_IDS[deptName] || null;
    // Gán doctorType dựa vào departmentID
    if (user.departmentID === 1) {
      user.doctorType = DOCTOR_TYPES.BLOOD_DEPARTMENT;
    } else {
      user.doctorType = DOCTOR_TYPES.OTHER_DEPARTMENT;
    }
  }
  return user;
});

// NOTE: Mock donation history - mostly replaced by real APIs
// Still used in some development/testing scenarios
export const mockDonationHistory = [
  {
    donationID: 1,
    userID: 1, // Nguyễn Văn A
    donationDate: "2024-01-10T09:00:00Z",
    bloodGroup: BLOOD_GROUPS.O,
    rhType: RH_TYPES.POSITIVE,
    componentType: "Whole",
    quantity: 450,
    isSuccessful: true,
    notes: "Hiến máu thành công, kết quả xét nghiệm tốt",
    // Additional fields for display
    bloodType: "O+",
    location: "Bệnh viện Đa khoa Ánh Dương",
    status: "completed",
    testResults: {
      hiv: "negative",
      hepatitisB: "negative",
      hepatitisC: "negative",
      syphilis: "negative",
    },
  },
  {
    donationID: 2,
    userID: 2, // Trần Thị B
    donationDate: "2024-01-08T11:30:00Z",
    bloodGroup: BLOOD_GROUPS.A,
    rhType: RH_TYPES.POSITIVE,
    componentType: "Whole",
    quantity: 450,
    isSuccessful: false,
    notes: "Đã khám sàng lọc, chờ lịch hiến máu",
    // Additional fields for display
    bloodType: "A+",
    location: "Bệnh viện XYZ",
    status: "medical_checked",
    testResults: null,
  },
  {
    donationID: 3,
    userID: 5, // Hoàng Văn E
    donationDate: "2023-10-15T14:00:00Z",
    bloodGroup: BLOOD_GROUPS.O,
    rhType: RH_TYPES.NEGATIVE,
    componentType: "Whole",
    quantity: 450,
    isSuccessful: true,
    notes: "Hiến máu định kỳ - máu hiếm O-",
    // Additional fields for display
    bloodType: "O-",
    location: "Bệnh viện XYZ",
    status: "completed",
    testResults: {
      hiv: "negative",
      hepatitisB: "negative",
      hepatitisC: "negative",
      syphilis: "negative",
    },
  },
];

// NOTE: mockBloodInventory đã được chuyển sang bloodInventoryConstants.js
// Use bloodInventoryConstants.js for inventory-related data and functions

// Mock hospital info matching database HospitalInfo table
export const mockHospitalInfo = {
  id: 1,
  name: "Bệnh viện Đa khoa Ánh Dương",
  address: "Đường Cách Mạng Tháng 8, Quận 3, TP.HCM, Vietnam",
  phone: "(028) 3957 1343",
  email: "<EMAIL>",
  workingHours: "Thứ 2 - Chủ nhật: 07:00 - 17:00",
  mapImageUrl:
    "https://maps.googleapis.com/maps/api/staticmap?center=10.7751237,106.6862143&zoom=15&size=600x400&key=YOUR_API_KEY",
  latitude: 10.7751237,
  longitude: 106.6862143,
};

// Function to get user by email and password
export const authenticateUser = (email, password) => {
  return mockUsers.find(
    (user) =>
      user.email === email &&
      user.password === password &&
      user.status === USER_STATUS.ACTIVE
  );
};

// Function to get user by ID
export const getUserById = (userID) => {
  return mockUsers.find((user) => user.userID === userID);
};

// NOTE: getBloodRequestsByRole đã được thay thế bằng API thực trong bloodRequestService
// Đã xóa function vì không còn sử dụng

// Function to get donation history by donor ID
export const getDonationHistoryByDonor = (userID) => {
  return mockDonationHistory.filter((donation) => donation.userID === userID);
};

// NOTE: getBloodInventoryWithStatus đã được chuyển sang bloodInventoryConstants.js
// Use bloodInventoryConstants.js for inventory-related functions

// NOTE: MOCK_BLOGS đã được thay thế bằng API thực trong bloodArticleService và newsService
// Đã xóa mock data vì không còn sử dụng
