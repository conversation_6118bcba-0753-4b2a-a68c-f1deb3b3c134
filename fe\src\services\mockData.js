// Mock data for Blood Management System based on database schema
// NOTE: Most data has been replaced by real APIs. This file is kept for:
// - Development/testing scenarios (DonationProcessManagement, ReportsManagement, etc.)
// - Constants and enums still in use (BLOOD_GROUPS, RH_TYPES, REQUEST_STATUS, etc.)
// - Backward compatibility and fallback data
// - Files still using mock data: DonationProcessManagement.jsx, NotificationsManagement.jsx,
//   ReportsManagement.jsx, ExternalRequestsManagement.jsx, EmergencyRequestsManagement.jsx

// User roles matching database Roles table
export const ROLES = {
  GUEST: "Guest",
  MEMBER: "1",
  STAFF_DOCTOR: "2",
  STAFF_BLOOD_MANAGER: "3",
  ADMIN: "4",
};

// User status matching database Users table
export const USER_STATUS = {
  INACTIVE: 0,
  ACTIVE: 1,
  BANNED: 2,
};

// Blood groups
export const BLOOD_GROUPS = {
  A: "A",
  B: "B",
  AB: "AB",
  O: "O",
};

// Rh types
export const RH_TYPES = {
  POSITIVE: "Rh+",
  NEGATIVE: "Rh-",
};

// Blood request status matching API specification
export const REQUEST_STATUS = {
  PENDING: 0, // Đang chờ xử lý
  ACCEPTED: 1, // Chấp nhận
  COMPLETED: 2, // Hoàn thành
  REJECTED: 3, // Từ chối
};

// Blood request urgency levels
export const URGENCY_LEVELS = {
  NORMAL: 0,
  URGENT: 1,
  CRITICAL: 2,
};

// NOTE: Blood component types moved to bloodInventoryConstants.js
// Import from bloodInventoryConstants.js instead

// Doctor types for different departments
export const DOCTOR_TYPES = {
  BLOOD_DEPARTMENT: "blood_department",
  OTHER_DEPARTMENT: "other_department",
};

// Department IDs mapping
export const DEPARTMENT_IDS = {
  "Khoa Huyết học": 1,
  "Khoa Tim mạch": 2,
  "Khoa Nhi": 3,
  "Khoa Cấp Cứu": 4,
  "Khoa Giải phẫu": 5,
  "Khoa Ngoại": 6,
};

// NOTE: mockUsers removed - replaced by real APIs
// Only ReportsManagement.jsx still uses this - should be updated to use real APIs

// NOTE: mockUsers array removed - replaced by real APIs
// Only ReportsManagement.jsx still uses this - should be updated to use real APIs
export const mockUsers = [];

// NOTE: Mock donation history - mostly replaced by real APIs
// Still used in some development/testing scenarios
export const mockDonationHistory = [];
// Helper functions for authentication and user management
export const authenticateUser = (email, password) => {
  return mockUsers.find(
    (user) => user.email === email && user.password === password
  );
};

export const getUserById = (id) => {
  return mockUsers.find((user) => user.userID === id || user.id === id);
};

export const getDonationHistoryByDonor = (userID) => {
  return mockDonationHistory.filter((donation) => donation.userID === userID);
};

// NOTE: Mock donation history - mostly replaced by real APIs
// Still used in some development/testing scenarios
export const mockDonationHistory = [
  {
    donationID: 1,
    userID: 1, // Nguyễn Văn A
    donationDate: "2024-01-10T09:00:00Z",
    bloodGroup: BLOOD_GROUPS.O,
    rhType: RH_TYPES.POSITIVE,
    componentType: "Whole",
    quantity: 450,
    isSuccessful: true,
    notes: "Hiến máu thành công, kết quả xét nghiệm tốt",
    // Additional fields for display
    bloodType: "O+",
    location: "Bệnh viện Đa khoa Ánh Dương",
    status: "completed",
    testResults: {
      hiv: "negative",
      hepatitisB: "negative",
      hepatitisC: "negative",
      syphilis: "negative",
    },
  },
  {
    donationID: 2,
    userID: 2, // Trần Thị B
    donationDate: "2024-01-08T11:30:00Z",
    bloodGroup: BLOOD_GROUPS.A,
    rhType: RH_TYPES.POSITIVE,
    componentType: "Whole",
    quantity: 450,
    isSuccessful: false,
    notes: "Đã khám sàng lọc, chờ lịch hiến máu",
    // Additional fields for display
    bloodType: "A+",
    location: "Bệnh viện XYZ",
    status: "medical_checked",
    testResults: null,
  },
  {
    donationID: 3,
    userID: 5, // Hoàng Văn E
    donationDate: "2023-10-15T14:00:00Z",
    bloodGroup: BLOOD_GROUPS.O,
    rhType: RH_TYPES.NEGATIVE,
    componentType: "Whole",
    quantity: 450,
    isSuccessful: true,
    notes: "Hiến máu định kỳ - máu hiếm O-",
    // Additional fields for display
    bloodType: "O-",
    location: "Bệnh viện XYZ",
    status: "completed",
    testResults: {
      hiv: "negative",
      hepatitisB: "negative",
      hepatitisC: "negative",
      syphilis: "negative",
    },
  },
];

// NOTE: mockBloodInventory đã được chuyển sang bloodInventoryConstants.js
// Use bloodInventoryConstants.js for inventory-related data and functions

// Mock hospital info matching database HospitalInfo table
export const mockHospitalInfo = {
  id: 1,
  name: "Bệnh viện Đa khoa Ánh Dương",
  address: "Đường Cách Mạng Tháng 8, Quận 3, TP.HCM, Vietnam",
  phone: "(028) 3957 1343",
  email: "<EMAIL>",
  workingHours: "Thứ 2 - Chủ nhật: 07:00 - 17:00",
  mapImageUrl:
    "https://maps.googleapis.com/maps/api/staticmap?center=10.7751237,106.6862143&zoom=15&size=600x400&key=YOUR_API_KEY",
  latitude: 10.7751237,
  longitude: 106.6862143,
};

// NOTE: getBloodInventoryWithStatus đã được chuyển sang bloodInventoryConstants.js
// Use bloodInventoryConstants.js for inventory-related functions

// NOTE: MOCK_BLOGS đã được thay thế bằng API thực trong bloodArticleService và newsService
// Đã xóa mock data vì không còn sử dụng
