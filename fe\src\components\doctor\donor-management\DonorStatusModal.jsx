import React from "react";
import {
  <PERSON><PERSON>, <PERSON><PERSON>, Card, Avatar, Row, Col, Descriptions, Badge,
  Divider, Select, Input, InputNumber, Tag
} from "antd";
import {
  EditOutlined, UserOutlined, CheckCircleOutlined
} from "@ant-design/icons";
import { StatusWorkflowService } from "../../../utils/statusWorkflowService";

const { TextArea } = Input;

/**
 * Modal cập nhật trạng thái hiến máu
 */
const DonorStatusModal = ({
  visible,
  onCancel,
  onSave,
  selectedDonor,
  statusUpdateData,
  setStatusUpdateData
}) => {

  const getTimeSlotText = (slot) => {
    if (slot === "morning" || slot === "Sáng (7:00-12:00)") {
      return "7:00 - 12:00";
    } else if (slot === "afternoon" || slot === "Chiều (13:00-17:00)") {
      return "13:00 - 17:00";
    }
    return slot || "N/A";
  };

  if (!selectedDonor) return null;

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <EditOutlined style={{ color: '#1890ff' }} />
          <span>Cập nhật trạng thái hiến máu</span>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width={700}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Hủy
        </Button>,
        <Button key="save" type="primary" onClick={onSave}>
          <CheckCircleOutlined /> Lưu cập nhật
        </Button>
      ]}
    >
      {/* Donor Summary */}
      <Card
        size="small"
        style={{ marginBottom: 16, backgroundColor: '#f8f9fa' }}
      >
        <Row gutter={16}>
          <Col span={8}>
            <div style={{ textAlign: 'center' }}>
              <Avatar size={64} icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />
              <div style={{ marginTop: 8, fontWeight: 'bold', color: '#1890ff' }}>
                {selectedDonor.name}
              </div>
            </div>
          </Col>
          <Col span={16}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Nhóm máu">
                <Tag color="red" style={{ fontSize: '14px' }}>
                  {selectedDonor.bloodType}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Trạng thái hiện tại">
                <Badge
                  color={StatusWorkflowService.getStatusInfo(selectedDonor.status).color}
                  text={StatusWorkflowService.getStatusInfo(selectedDonor.status).text}
                />
              </Descriptions.Item>
              <Descriptions.Item label="Ngày hẹn">
                {new Date(selectedDonor.appointmentDate).toLocaleDateString("vi-VN")} - {getTimeSlotText(selectedDonor.timeSlot)}
              </Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>

      {/* Status Update Section */}
      <Divider orientation="left">🔄 Cập nhật trạng thái</Divider>
      <div style={{ marginBottom: 16 }}>
        <label style={{ display: 'block', marginBottom: 8, fontWeight: 'bold' }}>
          Chọn trạng thái mới:
        </label>
        <Select
          style={{ width: '100%' }}
          value={statusUpdateData.status}
          onChange={(value) =>
            setStatusUpdateData((prev) => ({
              ...prev,
              status: value,
            }))
          }
          placeholder="Chọn trạng thái"
        >
          <Select.Option value={selectedDonor.status}>
            Giữ nguyên - {StatusWorkflowService.getStatusInfo(selectedDonor.status).text}
          </Select.Option>
          <Select.OptGroup label="Trạng thái cơ bản">
            <Select.Option value="0">
              <Badge color="#faad14" text="Đang chờ duyệt" />
            </Select.Option>
            <Select.Option value="1">
              <Badge color="#ff4d4f" text="Không chấp nhận" />
            </Select.Option>
            <Select.Option value="2">
              <Badge color="#52c41a" text="Chấp nhận" />
            </Select.Option>
            <Select.Option value="3">
              <Badge color="#d9d9d9" text="Hủy" />
            </Select.Option>
          </Select.OptGroup>
        </Select>
      </div>

      {/* Health Check Form - Show when status is approved or rejected */}
      {(statusUpdateData.status === "2" || statusUpdateData.status === 2 || statusUpdateData.status === "1" || statusUpdateData.status === 1) && (
        <div>
          <Divider orientation="left">🩺 Thông số sức khỏe</Divider>
          <Row gutter={16}>
            <Col span={12}>
              <div style={{ marginBottom: 16 }}>
                <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
                  Huyết áp (mmHg):
                </label>
                <Input
                  value={statusUpdateData.healthCheck.bloodPressure}
                  onChange={(e) =>
                    setStatusUpdateData((prev) => ({
                      ...prev,
                      healthCheck: {
                        ...prev.healthCheck,
                        bloodPressure: e.target.value,
                      },
                    }))
                  }
                  placeholder="120/80"
                />
              </div>
            </Col>
            <Col span={12}>
              <div style={{ marginBottom: 16 }}>
                <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
                  Nhịp tim (bpm):
                </label>
                <InputNumber
                  style={{ width: '100%' }}
                  value={statusUpdateData.healthCheck.heartRate}
                  onChange={(value) =>
                    setStatusUpdateData((prev) => ({
                      ...prev,
                      healthCheck: {
                        ...prev.healthCheck,
                        heartRate: value,
                      },
                    }))
                  }
                  placeholder="72"
                />
              </div>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <div style={{ marginBottom: 16 }}>
                <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
                  Hemoglobin (g/dL):
                </label>
                <InputNumber
                  style={{ width: '100%' }}
                  step={0.1}
                  value={statusUpdateData.healthCheck.hemoglobin}
                  onChange={(value) =>
                    setStatusUpdateData((prev) => ({
                      ...prev,
                      healthCheck: {
                        ...prev.healthCheck,
                        hemoglobin: value,
                      },
                    }))
                  }
                  placeholder="13.5"
                />
              </div>
            </Col>
            <Col span={12}>
              <div style={{ marginBottom: 16 }}>
                <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
                  Nhiệt độ (°C):
                </label>
                <InputNumber
                  style={{ width: '100%' }}
                  step={0.1}
                  value={statusUpdateData.healthCheck.temperature}
                  onChange={(value) =>
                    setStatusUpdateData((prev) => ({
                      ...prev,
                      healthCheck: {
                        ...prev.healthCheck,
                        temperature: value,
                      },
                    }))
                  }
                  placeholder="36.5"
                />
              </div>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <div style={{ marginBottom: 16 }}>
                <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
                  Cân nặng (kg):
                </label>
                <InputNumber
                  style={{ width: '100%' }}
                  value={statusUpdateData.healthCheck.weight}
                  onChange={(value) =>
                    setStatusUpdateData((prev) => ({
                      ...prev,
                      healthCheck: {
                        ...prev.healthCheck,
                        weight: value,
                      },
                    }))
                  }
                  placeholder="65"
                  min={30}
                  max={200}
                />
              </div>
            </Col>
            <Col span={12}>
              <div style={{ marginBottom: 16 }}>
                <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
                  Chiều cao (cm):
                </label>
                <InputNumber
                  style={{ width: '100%' }}
                  value={statusUpdateData.healthCheck.height}
                  onChange={(value) =>
                    setStatusUpdateData((prev) => ({
                      ...prev,
                      healthCheck: {
                        ...prev.healthCheck,
                        height: value,
                      },
                    }))
                  }
                  placeholder="170"
                  min={100}
                  max={250}
                />
              </div>
            </Col>
          </Row>
        </div>
      )}

      {/* Notes Section */}
      <Divider orientation="left">📝 Ghi chú</Divider>
      <TextArea
        value={statusUpdateData.notes}
        onChange={(e) =>
          setStatusUpdateData((prev) => ({
            ...prev,
            notes: e.target.value,
          }))
        }
        placeholder="Nhập ghi chú về tình trạng sức khỏe hoặc quá trình hiến máu..."
        rows={4}
        style={{ marginBottom: 16 }}
      />
    </Modal>
  );
};

export default DonorStatusModal;
