// Test script to verify component mapping
import { getBloodComponentName } from './src/constants/bloodInventoryConstants.js';

// Test data from API
const testData = {
  "requestId": 2,
  "userId": 2,
  "patientId": 2,
  "patientName": "Trần <PERSON>nh",
  "age": 39,
  "gender": "Nữ",
  "relationship": "Bạn bè",
  "facilityName": "Bệnh viện Bạch Mai",
  "doctorName": "<PERSON>ê Q<PERSON>ốc <PERSON>",
  "doctorPhone": "**********",
  "bloodGroup": "O",
  "rhType": "Rh+",
  "componentId": 3,
  "quantity": 1,
  "reason": "<PERSON>ấp cứu tai nạn",
  "status": 1,
  "createdTime": "2025-06-24T10:30:00"
};

console.log('Testing component mapping:');
console.log('componentId:', testData.componentId);
console.log('componentId type:', typeof testData.componentId);
console.log('Mapped name:', getBloodComponentName(testData.componentId));

// Test all possible values
console.log('\nAll mappings:');
for (let i = 1; i <= 4; i++) {
  console.log(`componentId ${i}:`, getBloodComponentName(i));
}

// Test string inputs
console.log('\nString inputs:');
console.log('componentId "3":', getBloodComponentName("3"));
console.log('componentId "invalid":', getBloodComponentName("invalid"));
console.log('componentId null:', getBloodComponentName(null));
console.log('componentId undefined:', getBloodComponentName(undefined));
