import config from '../config/environment';

class NotificationService {
  constructor() {
    this.baseURL = config.api.baseUrl;
  }
  // Mock data for notifications
  static mockNotifications = [
    {
      id: 1,
      userId: 1,
      type: 'donation_reminder',
      title: '🩸 Nhắc nhở hiến máu',
      message: 'Bạn đã có thể hiến máu trở lại! Lần hiến máu cuối của bạn là 60 ngày trước.',
      isRead: false,
      createdAt: '2024-12-10T08:00:00Z',
      data: {
        lastDonationDate: '2024-10-11',
        nextEligibleDate: '2024-12-06',
        bloodType: 'O+'
      }
    },
    {
      id: 2,
      userId: 1,
      type: 'urgent_request',
      title: '🚨 Yêu cầu máu khẩn cấp',
      message: '<PERSON>ầ<PERSON> gấp máu nhóm O+ tại bệnh viện. <PERSON>ạn có thể giúp đỡ không?',
      isRead: false,
      createdAt: '2024-12-09T14:30:00Z',
      data: {
        requestId: 123,
        bloodType: 'O+',
        quantity: '2 đơn vị',
        urgency: 'emergency',
        hospital: 'Bệnh viện XYZ'
      }
    },
    {
      id: 3,
      userId: 1,
      type: 'appointment_reminder',
      title: '📅 Nhắc nhở lịch hẹn',
      message: 'Bạn có lịch hẹn hiến máu vào ngày mai lúc 9:00 AM.',
      isRead: true,
      createdAt: '2024-12-08T18:00:00Z',
      data: {
        appointmentId: 456,
        appointmentDate: '2024-12-11T09:00:00Z',
        location: 'Bệnh viện XYZ - Tầng 2'
      }
    },
    {
      id: 4,
      userId: 1,
      type: 'health_check',
      title: '🏥 Kết quả khám sức khỏe',
      message: 'Kết quả khám sức khỏe của bạn đã có. Bạn đủ điều kiện hiến máu.',
      isRead: true,
      createdAt: '2024-12-07T10:15:00Z',
      data: {
        checkupId: 789,
        result: 'eligible',
        nextCheckupDate: '2024-06-07'
      }
    },
    {
      id: 5,
      userId: 1,
      type: 'donation_thanks',
      title: '💝 Cảm ơn bạn đã hiến máu',
      message: 'Cảm ơn bạn đã hiến máu hôm nay! Máu của bạn sẽ giúp cứu sống nhiều người.',
      isRead: true,
      createdAt: '2024-12-05T16:45:00Z',
      data: {
        donationId: 101,
        donationDate: '2024-12-05',
        bloodType: 'O+',
        quantity: '450ml'
      }
    },
    {
      id: 6,
      userId: 1,
      type: 'account_update',
      title: '👤 Hồ sơ cá nhân đã được cập nhật',
      message: 'Thông tin hồ sơ của bạn đã được cập nhật thành công.',
      isRead: false,
      createdAt: '2024-12-04T14:20:00Z',
      data: {
        changes: [
          {
            field: 'phone',
            oldValue: '**********',
            newValue: '**********'
          },
          {
            field: 'address',
            oldValue: '123 Đường ABC, Quận 1',
            newValue: '456 Đường XYZ, Quận 3'
          },
          {
            field: 'weight',
            oldValue: '65kg',
            newValue: '67kg'
          }
        ]
      }
    }
  ];

  // GET /api/Notification/user/{userId}
  static async getNotifications(userId) {
    try {
      const response = await fetch(`${config.api.baseUrl}/Notification/user/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Sort notifications by sent date (newest first)
      return data.sort((a, b) => {
        const dateA = new Date(a.SentAt || a.sentAt || a.createdAt);
        const dateB = new Date(b.SentAt || b.sentAt || b.createdAt);
        return dateB - dateA;
      });
    } catch (error) {
      console.error('Error getting notifications:', error);

      // Fallback to mock data if API fails
      return this.mockNotifications.filter(n => n.userId === parseInt(userId));
    }
  }

  // GET /api/Notification/{id}
  static async getNotificationById(notificationId) {
    try {
      const response = await fetch(`${config.api.baseUrl}/Notification/${notificationId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error getting notification by ID:', error);
      return null;
    }
  }

  // TODO: Replace with API call - GET /api/notifications/:userId/unread-count
  static async getUnreadCount(userId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const unreadCount = this.mockNotifications.filter(n => n.userId === userId && !n.isRead).length;
        resolve(unreadCount);
      }, 200);
    });
  }

  // PUT /api/Notification/mark-read/{id}
  static async markAsRead(notificationId) {
    try {
      const response = await fetch(`${config.api.baseUrl}/Notification/mark-read/${notificationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Check if response has content before parsing JSON
      const text = await response.text();
      if (text) {
        try {
          const data = JSON.parse(text);
          return data;
        } catch (jsonError) {
          return { success: true, message: 'Notification marked as read' };
        }
      } else {
        // Empty response, assume success
        return { success: true, message: 'Notification marked as read' };
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  // TODO: Replace with API call - PUT /api/notifications/:userId/mark-all-read
  static async markAllAsRead(userId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        this.mockNotifications.forEach(n => {
          if (n.userId === userId) {
            n.isRead = true;
          }
        });
        resolve(true);
      }, 500);
    });
  }

  // DELETE /api/Notification/{id}
  static async deleteNotification(notificationId) {
    try {
      const response = await fetch(`${config.api.baseUrl}/Notification/${notificationId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Check if response has content before parsing JSON
      const text = await response.text();
      if (text) {
        try {
          const data = JSON.parse(text);
          return data;
        } catch (jsonError) {
          return { success: true, message: 'Notification deleted successfully' };
        }
      } else {
        // Empty response, assume success
        return { success: true, message: 'Notification deleted successfully' };
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  // POST /api/Notification - Create notification
  static async createNotification(notification) {
    try {
      // Debug logging
      console.log('Creating notification for:', notification);

      // Match database schema: NotificationID, UserID, Title, Message, IsRead, SENT
      const payload = {
        UserID: parseInt(notification.userId),
        Title: notification.title,
        Message: notification.message,
        IsRead: false,
        SENT: new Date().toISOString()
      };

      console.log('Notification payload:', payload);

      const response = await fetch(`${config.api.baseUrl}/Notification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        // Log response details for debugging
        const errorText = await response.text();
        console.error('Notification API error response:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText
        });
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('Notification created successfully:', data);
      return data;
    } catch (error) {
      console.error('Error creating notification:', error);

      // Fallback to mock data if API fails
      const newNotification = {
        id: Date.now(),
        ...notification,
        isRead: false,
        createdAt: new Date().toISOString()
      };
      this.mockNotifications.push(newNotification);
      return newNotification;
    }
  }

  // Calculate next eligible donation date
  static calculateNextEligibleDate(lastDonationDate, gender = 'male') {
    const lastDate = new Date(lastDonationDate);
    const waitingPeriod = gender === 'female' ? 84 : 56; // days
    const nextDate = new Date(lastDate);
    nextDate.setDate(nextDate.getDate() + waitingPeriod);
    return nextDate;
  }

  // Check if user is eligible to donate
  static isEligibleToDonate(lastDonationDate, gender = 'male') {
    if (!lastDonationDate) return true;

    const nextEligibleDate = this.calculateNextEligibleDate(lastDonationDate, gender);
    return new Date() >= nextEligibleDate;
  }

  // Get days until next eligible donation
  static getDaysUntilEligible(lastDonationDate, gender = 'male') {
    if (!lastDonationDate) return 0;

    const nextEligibleDate = this.calculateNextEligibleDate(lastDonationDate, gender);
    const today = new Date();
    const diffTime = nextEligibleDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }

  // Send donation reminder notification
  static async sendDonationReminder(userId, userData) {
    const daysUntilEligible = this.getDaysUntilEligible(userData.lastDonationDate, userData.gender);

    if (daysUntilEligible <= 3 && daysUntilEligible >= 0) {
      const message = daysUntilEligible === 0
        ? 'Bạn đã có thể hiến máu trở lại! Hãy đăng ký lịch hẹn ngay.'
        : `Còn ${daysUntilEligible} ngày nữa bạn có thể hiến máu trở lại.`;

      return this.createNotification({
        userId,
        type: 'Reminder', // Database enum value
        title: '🩸 Nhắc nhở hiến máu',
        message,
        priority: false // Normal priority
      });
    }
    return null;
  }

  // Send urgent blood request notification
  static async sendUrgentBloodRequest(userId, requestData) {
    return this.createNotification({
      userId,
      type: 'Alert', // Database enum value for urgent requests
      title: '🚨 Yêu cầu máu khẩn cấp',
      message: `Cần gấp máu nhóm ${requestData.bloodType}. Bạn có thể giúp đỡ không?`,
      priority: true // High priority for urgent requests
    });
  }

  // Send appointment reminder notification
  static async sendAppointmentReminder(userId, appointmentData) {
    const appointmentDate = new Date(appointmentData.appointmentDate);
    const formattedDate = appointmentDate.toLocaleDateString('vi-VN');
    const formattedTime = appointmentDate.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit'
    });

    return this.createNotification({
      userId,
      type: 'Reminder', // Database enum value
      title: '📅 Nhắc nhở lịch hẹn',
      message: `Bạn có lịch hẹn hiến máu vào ${formattedDate} lúc ${formattedTime}.`,
      priority: false // Normal priority for appointment reminders
    });
  }

  // Blacklisted notification types that should not be displayed
  static BLACKLISTED_TYPES = [
    'delete_notification',
    'notification_deleted',
    'notification_delete',
    'delete_success',
    'removed_notification'
  ];

  // Blacklisted keywords in title/message
  static BLACKLISTED_KEYWORDS = [
    'delete notification',
    'xóa thông báo',
    'notification deleted',
    'thông báo đã xóa',
    'removed notification',
    'đã xóa thông báo'
  ];

  // Filter out deleted notifications and unwanted notification messages
  static filterActiveNotifications(notifications) {
    return notifications.filter(notification => {
      // Filter out soft deleted notifications
      const isDeleted = notification.isDeleted ||
        notification.IsDeleted ||
        notification.isDeleted === true ||
        notification.IsDeleted === true;

      // Filter out blacklisted notification types
      const notificationType = (notification.type || notification.Type || '').toLowerCase();
      const isBlacklistedType = this.BLACKLISTED_TYPES.includes(notificationType);

      // Filter out notifications with blacklisted keywords
      const hasBlacklistedKeywords = this.BLACKLISTED_KEYWORDS.some(keyword =>
        notification.message?.toLowerCase().includes(keyword) ||
        notification.title?.toLowerCase().includes(keyword)
      );

      return !isDeleted && !isBlacklistedType && !hasBlacklistedKeywords;
    });
  }

  // Format notification time
  static formatNotificationTime(createdAt) {
    if (!createdAt) return 'Chưa có thông tin';

    const now = new Date();
    const notificationTime = new Date(createdAt);

    // Check if date is valid
    if (isNaN(notificationTime.getTime())) {
      return 'Chưa có thông tin';
    }

    const diffMs = now - notificationTime;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Vừa xong';
    if (diffMins < 60) return `${diffMins} phút trước`;
    if (diffHours < 24) return `${diffHours} giờ trước`;
    if (diffDays < 7) return `${diffDays} ngày trước`;

    return notificationTime.toLocaleDateString('vi-VN');
  }

  // Get notification icon based on type
  static getNotificationIcon(type) {
    const icons = {
      // New database types
      'Reminder': '📅',
      'Alert': '🚨',
      'Report': '📋',
      // Legacy types
      donation_reminder: '🩸',
      urgent_request: '🚨',
      appointment_reminder: '📅',
      health_check: '🏥',
      donation_thanks: '💝',
      blood_request_update: '📋',
      system_announcement: '📢',
      account_update: '👤'
    };
    return icons[type] || '🔔';
  }

  // Get notification color based on type
  static getNotificationColor(type) {
    const colors = {
      // New database types
      'Reminder': '#17a2b8',
      'Alert': '#dc3545',
      'Report': '#6f42c1',
      // Legacy types
      donation_reminder: '#28a745',
      urgent_request: '#dc3545',
      appointment_reminder: '#17a2b8',
      health_check: '#6f42c1',
      donation_thanks: '#e83e8c',
      blood_request_update: '#ffc107',
      system_announcement: '#6c757d',
      account_update: '#3b82f6'
    };
    return colors[type] || '#6c757d';
  }

  // Event broadcasting for real-time notifications
  static notificationEventListeners = new Map();

  static addEventListener(eventType, callback) {
    if (!this.notificationEventListeners.has(eventType)) {
      this.notificationEventListeners.set(eventType, []);
    }
    this.notificationEventListeners.get(eventType).push(callback);
  }

  static removeEventListener(eventType, callback) {
    if (this.notificationEventListeners.has(eventType)) {
      const listeners = this.notificationEventListeners.get(eventType);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  static broadcastNotificationEvent(eventType, data) {
    if (this.notificationEventListeners.has(eventType)) {
      this.notificationEventListeners.get(eventType).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in notification event listener:', error);
        }
      });
    }
  }
}

export default NotificationService;
