import React, { useState, useEffect } from "react";
import {
  Mo<PERSON>,
  <PERSON>,
  Row,
  Col,
  Tag,
  Button,
  Spin,
  Typography,
  Space,
} from "antd";
import {
  UserOutlined,
  DropboxOutlined,
  MedicineBoxOutlined,
  CalendarOutlined,
  PhoneOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import authService from "../../services/authService";
import { bloodRequestService } from "../../services/bloodRequestService";
import userInfoService from "../../services/userInfoService";
import { classifyBloodRequest } from "../../utils/bloodRequestClassification";
import { getBloodComponentName } from "../../constants/bloodInventoryConstants";

const { Title, Text } = Typography;

const ManagerBloodRequestDetailModal = ({
  request,
  isOpen,
  onClose,
  onUpdate,
}) => {
  const [requestDetail, setRequestDetail] = useState(null);
  const [detailLoading, setDetailLoading] = useState(false);
  const [userInfo, setUserInfo] = useState(null);
  const [userLoading, setUserLoading] = useState(false);
  const [isFromMember, setIsFromMember] = useState(false);

  const currentUser = authService.getCurrentUser();

  // Load request detail when modal opens
  useEffect(() => {
    if (isOpen && (request?.requestId || request?.requestID)) {
      // Reset previous detail data
      setRequestDetail(null);
      setUserInfo(null);
      setIsFromMember(false);
      loadRequestDetail();
    } else if (!isOpen) {
      // Clear data when modal closes
      setRequestDetail(null);
      setUserInfo(null);
      setIsFromMember(false);
    }
  }, [isOpen, request?.requestId, request?.requestID]);

  const loadRequestDetail = async () => {
    try {
      setDetailLoading(true);
      // Use requestId (from API) or requestID (transformed data)
      const requestIdToUse = request.requestId || request.requestID;

      const response = await bloodRequestService.getBloodRequestById(
        requestIdToUse
      );

      if (response.success) {
        // Verify we got the correct data
        if (response.data.requestId == requestIdToUse) {
          setRequestDetail(response.data);
          // Determine if request is from member and load user info accordingly
          await determineRequestSource(response.data);
        } else {
          setRequestDetail(request);
          // Determine if request is from member and load user info accordingly
          await determineRequestSource(request);
        }
      } else {
        setRequestDetail(request);
        // Determine if request is from member and load user info accordingly
        await determineRequestSource(request);
      }
    } catch (error) {
      console.error("Error loading request detail:", error);
      setRequestDetail(request);
      // Determine if request is from member and load user info accordingly
      await determineRequestSource(request);
    } finally {
      setDetailLoading(false);
    }
  };

  const determineRequestSource = async (requestData) => {
    try {
      // First try to get user info if userID is valid
      if (requestData.userID && requestData.userID !== 0) {
        setUserLoading(true);
        try {
          const userResponse = await userInfoService.getUserInfo(
            requestData.userID
          );
          if (userResponse) {
            setUserInfo(userResponse);
            // roleID = 1: Member, roleID = 2: Doctor
            setIsFromMember(userResponse.roleID === 1);
            return;
          }
        } catch (error) {
          console.error("Error loading user info:", error);
        } finally {
          setUserLoading(false);
        }
      }

      // Fallback to classification if user info not available
      const classification = classifyBloodRequest(requestData);

      // If classified as external (member) request, try to load user info
      if (
        classification.isExternal ||
        classification.userRole === "Unknown-Member"
      ) {
        setIsFromMember(true);
        // Still try to load user info for member requests
        if (requestData.userID && requestData.userID !== 0) {
          setUserLoading(true);
          try {
            const userResponse = await userInfoService.getUserInfo(
              requestData.userID
            );
            if (userResponse) {
              setUserInfo(userResponse);
            }
          } catch (error) {
            console.error("Error loading user info for member:", error);
          } finally {
            setUserLoading(false);
          }
        }
      } else {
        // Internal request (from doctor) - don't show user info section
        setIsFromMember(false);
      }
    } catch (error) {
      console.error("Error determining request source:", error);
      // Default to member request to be safe
      setIsFromMember(true);
    }
  };

  if (!isOpen || !request) return null;

  const displayData = requestDetail || request;

  const getStatusColor = (status) => {
    switch (status) {
      case 0:
        return "orange"; // Pending - orange
      case 1:
        return "green"; // Accepted - green
      case 2:
        return "blue"; // Completed - blue
      case 3:
        return "red"; // Rejected - red
      case 4:
        return "default"; // Deleted - gray
      default:
        return "default";
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 0:
        return "Chờ duyệt";
      case 1:
        return "Đã chấp nhận";
      case 2:
        return "Hoàn thành";
      case 3:
        return "Từ chối";
      case 4:
        return "Đã xóa";
      default:
        return "Không xác định";
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "Không có thông tin";
    return new Date(dateString).toLocaleString("vi-VN");
  };

  return (
    <Modal
      title={
        <Space>
          <DropboxOutlined style={{ color: "#1976d2", fontSize: "20px" }} />
          <Title level={4} style={{ margin: 0, color: "#1976d2" }}>
            Chi tiết yêu cầu máu #
            {displayData.requestId || displayData.requestID || displayData.id}
          </Title>
        </Space>
      }
      open={isOpen}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose} type="primary">
          Đóng
        </Button>,
      ]}
      width={800}
      className="blood-request-detail-modal"
    >
      {detailLoading ? (
        <div style={{ textAlign: "center", padding: "40px" }}>
          <Spin size="large" />
          <div style={{ marginTop: "16px" }}>Đang tải chi tiết yêu cầu...</div>
        </div>
      ) : (
        <>
          {/* Status Badge */}
          <div style={{ textAlign: "center", marginBottom: "24px" }}>
            <Tag
              color={getStatusColor(displayData.status)}
              style={{ fontSize: "14px", padding: "8px 16px" }}
            >
              {getStatusText(displayData.status)}
            </Tag>
          </div>

          {/* Basic Request Information */}
          <Card
            title={
              <Space>
                <DropboxOutlined style={{ color: "#1976d2" }} />
                <span style={{ color: "#1976d2", fontWeight: 600 }}>
                  Thông tin yêu cầu máu
                </span>
              </Space>
            }
            style={{ marginBottom: "16px" }}
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div>
                  <Text strong style={{ color: "#000" }}>
                    Mã yêu cầu:
                  </Text>
                  <div style={{ marginTop: "8px" }}>
                    <Tag color="default">
                      #
                      {displayData.requestId ||
                        displayData.requestID ||
                        displayData.id}
                    </Tag>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <Text strong style={{ color: "#000" }}>
                    Nhóm máu:
                  </Text>
                  <div style={{ marginTop: "8px" }}>
                    <Tag
                      color="red"
                      style={{ fontSize: "16px", padding: "8px 12px" }}
                    >
                      {displayData.bloodGroup || "N/A"}
                      {displayData.rhType || ""}
                    </Tag>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <Text strong style={{ color: "#000" }}>
                    Số lượng:
                  </Text>
                  <div style={{ marginTop: "8px" }}>
                    <Tag
                      color="blue"
                      style={{ fontSize: "14px", padding: "6px 12px" }}
                    >
                      {displayData.quantity} ml
                    </Tag>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <Text strong style={{ color: "#000" }}>
                    Thành phần máu:
                  </Text>
                  <div style={{ marginTop: "8px" }}>
                    <Tag
                      color="purple"
                      style={{ fontSize: "14px", padding: "6px 12px" }}
                    >
                      {getBloodComponentName(displayData.componentId) ||
                        "Toàn phần"}
                    </Tag>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <Text strong style={{ color: "#000" }}>
                    Ngày tạo:
                  </Text>
                  <div
                    style={{
                      marginTop: "8px",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <CalendarOutlined
                      style={{ marginRight: "8px", color: "#000" }}
                    />
                    <Text style={{ color: "#000" }}>
                      {formatDate(displayData.createdTime)}
                    </Text>
                  </div>
                </div>
              </Col>
              <Col span={24}>
                <div>
                  <Text strong style={{ color: "#000" }}>
                    Lý do:
                  </Text>
                  <div
                    style={{
                      marginTop: "8px",
                      display: "flex",
                      alignItems: "flex-start",
                    }}
                  >
                    <FileTextOutlined
                      style={{
                        marginRight: "8px",
                        marginTop: "4px",
                        color: "#000",
                      }}
                    />
                    <Text style={{ color: "#000" }}>
                      {displayData.reason || "Không có thông tin"}
                    </Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>

          {/* Patient Information */}
          <Card
            title={
              <Space>
                <UserOutlined style={{ color: "#1976d2" }} />
                <span style={{ color: "#1976d2", fontWeight: 600 }}>
                  Thông tin bệnh nhân
                </span>
              </Space>
            }
            style={{ marginBottom: "16px" }}
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div>
                  <Text strong style={{ color: "#000" }}>
                    Mã bệnh nhân:
                  </Text>
                  <div
                    style={{
                      marginTop: "8px",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <InfoCircleOutlined
                      style={{ marginRight: "8px", color: "#000" }}
                    />
                    <Text style={{ color: "#000" }}>
                      {displayData.patientId || "Không có"}
                    </Text>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <Text strong style={{ color: "#000" }}>
                    Tên bệnh nhân:
                  </Text>
                  <div
                    style={{
                      marginTop: "8px",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <UserOutlined
                      style={{ marginRight: "8px", color: "#000" }}
                    />
                    <Text strong style={{ color: "#000" }}>
                      {displayData.patientName || "Không có"}
                    </Text>
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div>
                  <Text strong style={{ color: "#000" }}>
                    Tuổi:
                  </Text>
                  <div style={{ marginTop: "8px" }}>
                    <Tag color="geekblue">{displayData.age || "N/A"} tuổi</Tag>
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div>
                  <Text strong style={{ color: "#000" }}>
                    Giới tính:
                  </Text>
                  <div style={{ marginTop: "8px" }}>
                    <Tag color="purple">{displayData.gender || "N/A"}</Tag>
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div>
                  <Text strong style={{ color: "#000" }}>
                    Mối quan hệ:
                  </Text>
                  <div style={{ marginTop: "8px" }}>
                    <Tag color="cyan">{displayData.relationship || "N/A"}</Tag>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>

          {/* Doctor & Facility Information */}
          <Card
            title={
              <Space>
                <MedicineBoxOutlined style={{ color: "#1976d2" }} />
                <span style={{ color: "#1976d2", fontWeight: 600 }}>
                  Thông tin bác sĩ & cơ sở
                </span>
              </Space>
            }
            style={{ marginBottom: isFromMember ? "16px" : "0" }}
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div>
                  <Text strong style={{ color: "#000" }}>
                    Bác sĩ yêu cầu:
                  </Text>
                  <div
                    style={{
                      marginTop: "8px",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <UserOutlined
                      style={{ marginRight: "8px", color: "#000" }}
                    />
                    <Text strong style={{ color: "#000" }}>
                      {displayData.doctorName || "Không có"}
                    </Text>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <Text strong style={{ color: "#000" }}>
                    Số điện thoại:
                  </Text>
                  <div
                    style={{
                      marginTop: "8px",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <PhoneOutlined
                      style={{ marginRight: "8px", color: "#000" }}
                    />
                    <Text style={{ color: "#000" }}>
                      {displayData.doctorPhone || "Không có"}
                    </Text>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <Text strong style={{ color: "#000" }}>
                    Cơ sở y tế:
                  </Text>
                  <div
                    style={{
                      marginTop: "8px",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <MedicineBoxOutlined
                      style={{ marginRight: "8px", color: "#000" }}
                    />
                    <Text strong style={{ color: "#000" }}>
                      {displayData.facilityName || "Không có"}
                    </Text>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <Text strong style={{ color: "#000" }}>
                    Mã người dùng:
                  </Text>
                  <div style={{ marginTop: "8px" }}>
                    <Tag color="default">#{displayData.userID || "N/A"}</Tag>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>

          {/* User Information - Only show for member requests */}
          {isFromMember && (
            <Card
              title={
                <Space>
                  <UserOutlined style={{ color: "#1976d2" }} />
                  <span style={{ color: "#1976d2", fontWeight: 600 }}>
                    Thông tin người điền form
                  </span>
                </Space>
              }
              style={{ marginBottom: "16px" }}
            >
              {userLoading ? (
                <div style={{ textAlign: "center", padding: "20px" }}>
                  <Spin size="small" />
                  <div
                    style={{
                      marginTop: "8px",
                      fontSize: "12px",
                      color: "#666",
                    }}
                  >
                    Đang tải thông tin người dùng...
                  </div>
                </div>
              ) : userInfo ? (
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <div>
                      <Text strong style={{ color: "#000" }}>
                        Họ và tên:
                      </Text>
                      <div
                        style={{
                          marginTop: "8px",
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <UserOutlined
                          style={{ marginRight: "8px", color: "#000" }}
                        />
                        <Text strong style={{ color: "#000" }}>
                          {userInfo.name || userInfo.fullName || "Không có"}
                        </Text>
                      </div>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div>
                      <Text strong style={{ color: "#000" }}>
                        Email:
                      </Text>
                      <div
                        style={{
                          marginTop: "8px",
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <Text style={{ color: "#000" }}>
                          {userInfo.email || "Không có"}
                        </Text>
                      </div>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div>
                      <Text strong style={{ color: "#000" }}>
                        Số điện thoại:
                      </Text>
                      <div
                        style={{
                          marginTop: "8px",
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <PhoneOutlined
                          style={{ marginRight: "8px", color: "#000" }}
                        />
                        <Text style={{ color: "#000" }}>
                          {userInfo.phone || userInfo.phoneNumber || "Không có"}
                        </Text>
                      </div>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div>
                      <Text strong style={{ color: "#000" }}>
                        Nhóm máu:
                      </Text>
                      <div style={{ marginTop: "8px" }}>
                        <Tag
                          color="red"
                          style={{ fontSize: "14px", padding: "6px 12px" }}
                        >
                          {userInfo.bloodGroup || "N/A"}
                          {userInfo.rhType || ""}
                        </Tag>
                      </div>
                    </div>
                  </Col>
                  <Col span={8}>
                    <div>
                      <Text strong style={{ color: "#000" }}>
                        Tuổi:
                      </Text>
                      <div style={{ marginTop: "8px" }}>
                        <Tag color="geekblue">{userInfo.age || "N/A"} tuổi</Tag>
                      </div>
                    </div>
                  </Col>
                  <Col span={8}>
                    <div>
                      <Text strong style={{ color: "#000" }}>
                        Giới tính:
                      </Text>
                      <div style={{ marginTop: "8px" }}>
                        <Tag color="purple">{userInfo.gender || "N/A"}</Tag>
                      </div>
                    </div>
                  </Col>
                  <Col span={8}>
                    <div>
                      <Text strong style={{ color: "#000" }}>
                        CCCD/CMND:
                      </Text>
                      <div style={{ marginTop: "8px" }}>
                        <Text style={{ color: "#000" }}>
                          {userInfo.idCard || "Không có"}
                        </Text>
                      </div>
                    </div>
                  </Col>
                  <Col span={24}>
                    <div>
                      <Text strong style={{ color: "#000" }}>
                        Địa chỉ:
                      </Text>
                      <div
                        style={{
                          marginTop: "8px",
                          display: "flex",
                          alignItems: "flex-start",
                        }}
                      >
                        <Text style={{ color: "#000" }}>
                          {userInfo.address ||
                            `${userInfo.ward || ""} ${
                              userInfo.district || ""
                            } ${userInfo.city || ""}`.trim() ||
                            "Không có"}
                        </Text>
                      </div>
                    </div>
                  </Col>
                </Row>
              ) : (
                <div
                  style={{
                    textAlign: "center",
                    padding: "20px",
                    color: "#666",
                  }}
                >
                  <InfoCircleOutlined
                    style={{ fontSize: "24px", marginBottom: "8px" }}
                  />
                  <div>Không thể tải thông tin người dùng</div>
                  <div style={{ fontSize: "12px", marginTop: "4px" }}>
                    User ID: {displayData.userID || "N/A"}
                  </div>
                </div>
              )}
            </Card>
          )}
        </>
      )}
    </Modal>
  );
};

export default ManagerBloodRequestDetailModal;
