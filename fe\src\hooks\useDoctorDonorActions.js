import { message } from "antd";
import bloodDonationService from "../services/bloodDonationService";
import NotificationService from "../services/notificationService";

/**
 * Custom hook for handling doctor donor management actions (save, update, etc.)
 */
export const useDoctorDonorActions = () => {

  // Save donor info update
  const handleSaveUpdate = async (selectedDonor, updateData, currentUser, setDonors, setShowUpdateModal, setSelectedDonor) => {
    if (!selectedDonor) return;

    try {
      const updatedDonor = {
        ...selectedDonor,
        bloodType: updateData.bloodType,
        healthStatus: updateData.healthStatus,
        bloodRelatedDiseases: updateData.bloodRelatedDiseases,
        notes: updateData.notes,
        testResults: updateData.testResults,
        healthSurvey: {
          ...selectedDonor.healthSurvey,
          chronicDiseases: updateData.chronicDiseases,
        },
        updatedAt: new Date().toISOString(),
        updatedBy: currentUser?.name,
      };

      setDonors((prev) =>
        prev.map((d) => (d.id === selectedDonor.id ? updatedDonor : d))
      );

      setShowUpdateModal(false);
      setSelectedDonor(null);
      message.success("Cập nhật thông tin thành công!");
    } catch (error) {
      console.error("Error updating donor:", error);
      message.error("Có lỗi xảy ra khi cập nhật thông tin!");
    }
  };

  // Save status update
  const handleSaveStatusUpdate = async (selectedDonor, statusUpdateData, currentUser, setDonors, setShowStatusModal, setSelectedDonor) => {
    if (!selectedDonor) return;

    try {
      const statusToSend = typeof statusUpdateData.status === 'string' ?
        parseInt(statusUpdateData.status) : statusUpdateData.status;

      console.log('Updating appointment:', {
        appointmentId: selectedDonor.id,
        status: statusToSend,
        notes: statusUpdateData.notes,
        healthCheck: statusUpdateData.healthCheck,
        originalDonor: selectedDonor
      });

      // 1. Update appointment status using PATCH endpoint
      try {
        await bloodDonationService.updateAppointmentStatus(
          selectedDonor.id,
          statusToSend,
          statusUpdateData.notes
        );
        console.log('✅ Successfully updated appointment status');
      } catch (statusError) {
        console.error('❌ Failed to update appointment status:', statusError);
        throw statusError;
      }

      // 2. Update doctor health check data via POST /api/Appointment/doctor-update/{id}
      const hasHealthCheckData = statusUpdateData.healthCheck.bloodPressure ||
        statusUpdateData.healthCheck.heartRate ||
        statusUpdateData.healthCheck.hemoglobin ||
        statusUpdateData.healthCheck.temperature ||
        (statusUpdateData.notes && statusUpdateData.notes.trim());

      if (hasHealthCheckData) {
        try {
          const doctorUpdateData = {
            // Health check data for doctor-update API - match backend schema
            notes: statusUpdateData.notes?.trim() || "",
            bloodPressure: statusUpdateData.healthCheck.bloodPressure || "",
            heartRate: statusUpdateData.healthCheck.heartRate ? Number(statusUpdateData.healthCheck.heartRate) : 0,
            hemoglobin: statusUpdateData.healthCheck.hemoglobin ? Number(statusUpdateData.healthCheck.hemoglobin) : 0,
            temperature: statusUpdateData.healthCheck.temperature ? Number(statusUpdateData.healthCheck.temperature) : 0,
            doctorId: currentUser?.id ? Number(currentUser.id) : 0
          };

          console.log('Updating health check data via doctor-update API:', doctorUpdateData);
          await bloodDonationService.updateAppointmentDoctorData(
            selectedDonor.id,
            doctorUpdateData
          );
          console.log('✅ Successfully updated health check data');
        } catch (doctorUpdateError) {
          console.warn('⚠️ Failed to update health check data:', doctorUpdateError);
        }
      }

      // 3. Update user information (weight, height) via PUT /api/Information/{id}
      const hasWeightUpdate = statusUpdateData.healthCheck.weight && statusUpdateData.healthCheck.weight > 0;
      const hasHeightUpdate = statusUpdateData.healthCheck.height && statusUpdateData.healthCheck.height > 0;

      if ((hasWeightUpdate || hasHeightUpdate) && selectedDonor.userId) {
        try {
          console.log('Fetching current user info for update...');
          const currentUserInfo = await bloodDonationService.getUserInfo(selectedDonor.userId);

          const userInfoToUpdate = {
            userID: selectedDonor.userId,
            email: currentUserInfo.Email || currentUserInfo.email || "",
            password: currentUserInfo.Password || "Ab1234@",
            phone: currentUserInfo.Phone || currentUserInfo.phone || "",
            idCardType: currentUserInfo.IdCardType || currentUserInfo.idCardType || "",
            idCard: currentUserInfo.IdCard || currentUserInfo.idCard || "",
            name: currentUserInfo.Name || currentUserInfo.name || "",
            dateOfBirth: currentUserInfo.DateOfBirth || currentUserInfo.dateOfBirth || null,
            age: currentUserInfo.Age || currentUserInfo.age || null,
            gender: currentUserInfo.Gender || currentUserInfo.gender || "",
            city: currentUserInfo.City || currentUserInfo.city || "",
            district: currentUserInfo.District || currentUserInfo.district || "",
            ward: currentUserInfo.Ward || currentUserInfo.ward || "",
            address: currentUserInfo.Address || currentUserInfo.address || "",
            distance: currentUserInfo.Distance || currentUserInfo.distance || null,
            bloodGroup: currentUserInfo.BloodGroup || currentUserInfo.bloodGroup || "",
            rhType: currentUserInfo.RhType || currentUserInfo.rhType || "",
            weight: hasWeightUpdate ? Number(statusUpdateData.healthCheck.weight) : (currentUserInfo.Weight || currentUserInfo.weight || null),
            height: hasHeightUpdate ? Number(statusUpdateData.healthCheck.height) : (currentUserInfo.Height || currentUserInfo.height || null),
            status: currentUserInfo.Status || currentUserInfo.status || 1,
            roleID: currentUserInfo.RoleID || currentUserInfo.roleID || 1,
            departmentId: currentUserInfo.DepartmentId || currentUserInfo.departmentId || null,
            createdAt: currentUserInfo.CreatedAt || currentUserInfo.createdAt || new Date().toISOString(),
            // Flag to indicate this is a doctor update - should not trigger profile update notification
            isDoctorUpdate: true,
            suppressNotification: true
          };

          console.log('Attempting to update user information:', { userId: selectedDonor.userId, data: userInfoToUpdate });
          await bloodDonationService.updateUserInformation(selectedDonor.userId, userInfoToUpdate);
          console.log('✅ Successfully updated user information');
        } catch (userUpdateError) {
          console.warn('⚠️ Failed to update user information, continuing:', userUpdateError);
        }
      }

      // Update local state with all changes including health check data
      const updatedDonor = {
        ...selectedDonor,
        status: statusToSend,
        notes: statusUpdateData.notes,
        testResults: {
          ...selectedDonor.testResults,
          ...statusUpdateData.healthCheck
        },
        weight: statusUpdateData.healthCheck.weight || selectedDonor.weight,
        height: statusUpdateData.healthCheck.height || selectedDonor.height,
        updatedBy: currentUser?.name,
        updatedAt: new Date().toISOString(),
        ...(statusToSend === 2 && {
          approvedAt: new Date().toISOString(),
        }),
        ...(statusToSend === 1 && {
          rejectedAt: new Date().toISOString(),
        }),
        ...(statusToSend === 3 && {
          cancelledAt: new Date().toISOString(),
        }),
      };

      setDonors((prev) =>
        prev.map((donor) =>
          donor.id === selectedDonor.id ? updatedDonor : donor
        )
      );

      setShowStatusModal(false);
      setSelectedDonor(null);
      message.success("Cập nhật trạng thái thành công!");

      // Send notifications based on status - ONLY status update notifications
      if (statusToSend === 1) { // Rejected
        try {
          await NotificationService.createNotification({
            userId: selectedDonor.userId, // Fix: Use userId instead of id
            type: "donation_status_update",
            title: "📋 Cập nhật trạng thái hiến máu",
            message: "Cảm ơn bạn đã đăng ký hiến máu. Mặc dù lần này chưa phù hợp nhưng chúng tôi rất trân trọng tinh thần của bạn.",
            data: {
              appointmentId: selectedDonor.id,
              status: "Không chấp nhận",
              updateDate: new Date().toISOString().split("T")[0],
            },
          });
        } catch (notificationError) {
          console.warn("Could not send status notification:", notificationError);
        }
      } else if (statusToSend === 2) { // Approved
        try {
          await NotificationService.createNotification({
            userId: selectedDonor.userId, // Fix: Use userId instead of id
            type: "donation_status_update",
            title: "📋 Cập nhật trạng thái hiến máu",
            message: "Đăng ký hiến máu của bạn đã được chấp nhận. Vui lòng đến đúng giờ hẹn.",
            data: {
              appointmentId: selectedDonor.id,
              status: "Chấp nhận",
              updateDate: new Date().toISOString().split("T")[0],
            },
          });
        } catch (notificationError) {
          console.warn("Could not send status notification:", notificationError);
        }
      } else if (statusToSend === 3) { // Cancelled
        try {
          await NotificationService.createNotification({
            userId: selectedDonor.userId,
            type: "donation_status_update",
            title: "📋 Cập nhật trạng thái hiến máu",
            message: "Lịch hẹn hiến máu của bạn đã được hủy. Bạn có thể đăng ký lại vào lần khác.",
            data: {
              appointmentId: selectedDonor.id,
              status: "Đã hủy",
              updateDate: new Date().toISOString().split("T")[0],
            },
          });
        } catch (notificationError) {
          console.warn("Could not send status notification:", notificationError);
        }
      }
    } catch (error) {
      console.error("Error updating donor status:", error);
      message.error("Có lỗi xảy ra khi cập nhật trạng thái!");
    }
  };

  return {
    handleSaveUpdate,
    handleSaveStatusUpdate,
  };
};
