import React, { useState } from "react";
import { Table, Button, Space, Tooltip } from "antd";
import {
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  CalendarOutlined,
} from "@ant-design/icons";
import {
  getStatusText,
  getStatusColor,
  formatDate,
} from "../../../utils/bloodRequestUtils";
import RejectRequestModal from "./RejectRequestModal";
import "../../../styles/components/RejectRequestModal.scss";
import "../../../styles/components/BloodRequestTable.scss";

/**
 * Table component for displaying blood requests
 */
const DoctorBloodRequestsTable = ({
  data,
  loading,
  onViewDetails,
  onApprove,
  onReject,
  // onComplete, // Removed - no longer needed for hematology doctors
  isBloodDepartment,
  activeTab,
}) => {
  // State for reject modal
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [selectedRequestForReject, setSelectedRequestForReject] =
    useState(null);
  const [rejectLoading, setRejectLoading] = useState(false);

  // Handle reject button click
  const handleRejectClick = (request) => {
    setSelectedRequestForReject(request);
    setShowRejectModal(true);
  };

  // Handle reject confirmation with reason
  const handleRejectConfirm = async (reason) => {
    if (selectedRequestForReject && onReject) {
      setRejectLoading(true);
      try {
        await onReject(selectedRequestForReject.id, reason);
      } catch (error) {
        console.error("Error rejecting request:", error);
      } finally {
        setRejectLoading(false);
      }
    }
  };

  // Handle reject modal close
  const handleRejectModalClose = () => {
    setShowRejectModal(false);
    setSelectedRequestForReject(null);
  };

  // Table columns configuration
  const columns = [
    {
      title: "Mã yêu cầu",
      dataIndex: "requestID",
      key: "requestID",
      width: 100,
      render: (id) => <span className="request-id">#{id}</span>,
    },
    {
      title: "Bệnh nhân",
      dataIndex: "patientInfo",
      key: "patientInfo",
      width: 150,
      render: (patientInfo) => (
        <div className="patient-info">
          <div className="patient-name">{patientInfo?.name}</div>
          <div className="patient-details">
            <span className="detail-item">{patientInfo?.age} tuổi</span>
            <span className="detail-item">{patientInfo?.gender}</span>
          </div>
        </div>
      ),
    },
    {
      title: "Nhóm máu",
      dataIndex: "bloodType",
      key: "bloodType",
      width: 80,
      render: (bloodType) => {
        const isPositive = bloodType?.includes("+");
        return (
          <span
            className={`blood-type-tag ${isPositive ? "positive" : "negative"}`}
          >
            {bloodType}
          </span>
        );
      },
    },
    {
      title: "Số lượng",
      dataIndex: "quantity",
      key: "quantity",
      width: 80,
      render: (quantity) => (
        <span className="quantity-display">
          {quantity}
          <span className="unit">ml</span>
        </span>
      ),
    },
    {
      title: "Thành phần",
      dataIndex: "componentType",
      key: "componentType",
      width: 100,
      render: (componentType, record) => (
        <span className="component-display">
          {componentType || record.componentId || "Toàn phần"}
        </span>
      ),
    },
    {
      title:
        isBloodDepartment && activeTab === "external"
          ? "Người yêu cầu"
          : "Bác sĩ",
      dataIndex: "doctorInfo",
      key: "doctorInfo",
      width: 120,
      render: (doctorInfo) => (
        <div className="doctor-info">
          <div className="doctor-name">{doctorInfo?.name || "N/A"}</div>
          {doctorInfo?.department && (
            <div className="doctor-department">{doctorInfo.department}</div>
          )}
        </div>
      ),
    },
    {
      title: "Ngày tạo",
      dataIndex: "createdTime",
      key: "createdTime",
      width: 110,
      render: (time) => (
        <div className="date-display">
          <CalendarOutlined className="date-icon" />
          {formatDate(time)}
        </div>
      ),
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status) => (
        <span className={`status-badge status-${getStatusColor(status)}`}>
          {getStatusText(status)}
        </span>
      ),
    },
    {
      title: "Hành động",
      key: "actions",
      width: 120,
      render: (_, request) => {
        const actionButtonStyle = {
          borderRadius: "8px",
          border: "none",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          transition: "all 0.2s ease",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        };

        const viewButtonStyle = {
          ...actionButtonStyle,
          background: "#e6f7ff",
          color: "#1890ff",
          border: "1px solid #91d5ff",
        };

        const acceptButtonStyle = {
          ...actionButtonStyle,
          background: "#f6ffed",
          color: "#52c41a",
          border: "1px solid #b7eb8f",
        };

        const rejectButtonStyle = {
          ...actionButtonStyle,
          background: "#fff2f0",
          color: "#ff4d4f",
          border: "1px solid #ffccc7",
        };

        return (
          <Space size="small" className="action-buttons">
            {/* Always show Details button */}
            <Tooltip title="Xem chi tiết">
              <Button
                icon={<EyeOutlined />}
                onClick={() => onViewDetails(request)}
                size="small"
                style={viewButtonStyle}
                className="view-btn"
              />
            </Tooltip>

            {/* Only show action buttons for hematology department */}
            {isBloodDepartment && request.status === 0 && (
              <>
                <Tooltip title="Chấp nhận yêu cầu">
                  <Button
                    icon={<CheckOutlined />}
                    onClick={() => onApprove(request.id || request.requestId)}
                    size="small"
                    style={acceptButtonStyle}
                    className="accept-btn"
                  />
                </Tooltip>
                <Tooltip title="Từ chối yêu cầu">
                  <Button
                    icon={<CloseOutlined />}
                    onClick={() => handleRejectClick(request)}
                    size="small"
                    style={rejectButtonStyle}
                    className="reject-btn"
                  />
                </Tooltip>
              </>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <Table
        className="blood-request-table"
        columns={columns}
        dataSource={data}
        rowKey="requestID"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} của ${total} yêu cầu`,
        }}
      />

      {/* Reject Request Modal */}
      <RejectRequestModal
        isOpen={showRejectModal}
        onClose={handleRejectModalClose}
        onConfirm={handleRejectConfirm}
        loading={rejectLoading}
        requestInfo={selectedRequestForReject}
      />
    </>
  );
};

export default DoctorBloodRequestsTable;
